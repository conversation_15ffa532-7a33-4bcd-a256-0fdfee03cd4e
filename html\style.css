/* Custom Font */

@font-face {
	font-family: Valo;
	src: url(valo.ttf);
}

/* Global Enhancements */
body {
	background: radial-gradient(ellipse at center,
		rgba(20, 20, 30, 0.1) 0%,
		rgba(10, 10, 20, 0.05) 70%,
		transparent 100%);
}

/* Scrollbar Styling */
#killfeed-container::-webkit-scrollbar {
	width: 4px;
}

#killfeed-container::-webkit-scrollbar-track {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 2px;
}

#killfeed-container::-webkit-scrollbar-thumb {
	background: linear-gradient(to bottom,
		rgba(255, 255, 255, 0.3),
		rgba(255, 255, 255, 0.1));
	border-radius: 2px;
}

#killfeed-container::-webkit-scrollbar-thumb:hover {
	background: linear-gradient(to bottom,
		rgba(255, 255, 255, 0.5),
		rgba(255, 255, 255, 0.2));
}

#killfeed-container {
	margin-top: 15%;
	margin-left: 2vh;
	width: 45vw;
	height: 65vh;
	float: left;
	overflow-y: hidden;
	overflow-x: hidden;
	position: relative;
	z-index: 10;
}

#killfeed-container::before {
	content: '';
	position: absolute;
	top: -10px;
	left: -10px;
	right: -10px;
	bottom: -10px;
	background: linear-gradient(45deg,
		rgba(255, 0, 150, 0.1) 0%,
		rgba(0, 255, 255, 0.1) 25%,
		rgba(255, 255, 0, 0.1) 50%,
		rgba(150, 0, 255, 0.1) 75%,
		rgba(255, 0, 150, 0.1) 100%);
	border-radius: 15px;
	filter: blur(15px);
	opacity: 0;
	animation: ambientGlow 8s ease-in-out infinite;
	z-index: -1;
}

@keyframes ambientGlow {
	0%, 100% { opacity: 0; }
	50% { opacity: 0.3; }
}

.kill-line {
	float: left;
	width: 100%;
	margin-bottom: 0.8vh;
	animation-fill-mode: forwards;
	position: relative;
	transform-origin: left center;
}

.kill-line::before {
	content: '';
	position: absolute;
	left: -5px;
	top: 50%;
	transform: translateY(-50%);
	width: 4px;
	height: 80%;
	background: linear-gradient(to bottom,
		rgba(255, 255, 255, 0.8),
		rgba(255, 255, 255, 0.2));
	border-radius: 2px;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.kill-line:hover::before {
	opacity: 1;
}

.kill-container {
	min-height: 3.5vh;
	float: left;
	display: inline-flex;
	align-items: center;
	justify-content: flex-start;
	position: relative;
	overflow: hidden;
}

.kill-container::after {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg,
		transparent,
		rgba(255, 255, 255, 0.2),
		transparent);
	transition: left 0.6s ease;
}

.kill-container:hover::after {
	left: 100%;
}

.line-clamp {
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;  
	overflow: hidden;
}

.black-design {
	background: linear-gradient(135deg,
		rgba(20, 20, 25, 0.95) 0%,
		rgba(35, 35, 45, 0.9) 50%,
		rgba(25, 25, 35, 0.95) 100%);
	border-radius: 12px;
	padding: 0.6em 0.8em;
	box-shadow:
		0 8px 32px rgba(0, 0, 0, 0.4),
		0 2px 8px rgba(0, 0, 0, 0.3),
		inset 0 1px 0 rgba(255, 255, 255, 0.1),
		inset 0 -1px 0 rgba(0, 0, 0, 0.2);
	backdrop-filter: blur(10px) saturate(1.2);
	border: 1px solid rgba(255, 255, 255, 0.08);
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
}

.black-design::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg,
		rgba(100, 100, 120, 0.1) 0%,
		transparent 50%,
		rgba(80, 80, 100, 0.1) 100%);
	border-radius: 12px;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.black-design:hover {
	transform: translateY(-3px) scale(1.02);
	box-shadow:
		0 12px 40px rgba(0, 0, 0, 0.5),
		0 4px 12px rgba(0, 0, 0, 0.4),
		inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.black-design:hover::before {
	opacity: 1;
}

.teal-design {
	background: linear-gradient(135deg,
		rgba(120, 20, 140, 0.95) 0%,
		rgba(160, 40, 180, 0.9) 30%,
		rgba(140, 60, 200, 0.9) 70%,
		rgba(110, 30, 160, 0.95) 100%);
	border-radius: 12px;
	padding: 0.6em 0.8em;
	box-shadow:
		0 8px 32px rgba(140, 30, 160, 0.4),
		0 2px 8px rgba(160, 40, 180, 0.3),
		inset 0 1px 0 rgba(255, 255, 255, 0.15),
		inset 0 -1px 0 rgba(100, 20, 120, 0.3);
	backdrop-filter: blur(10px) saturate(1.3);
	border: 1px solid rgba(180, 60, 200, 0.4);
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
}

.teal-design::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg,
		rgba(200, 100, 255, 0.2) 0%,
		transparent 50%,
		rgba(150, 80, 220, 0.2) 100%);
	border-radius: 12px;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.teal-design:hover {
	transform: translateY(-3px) scale(1.02);
	box-shadow:
		0 12px 40px rgba(140, 30, 160, 0.6),
		0 4px 12px rgba(160, 40, 180, 0.5),
		inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.teal-design:hover::before {
	opacity: 1;
}

.red-design {
	background: linear-gradient(135deg,
		rgba(200, 140, 20, 0.95) 0%,
		rgba(240, 180, 40, 0.9) 30%,
		rgba(220, 160, 60, 0.9) 70%,
		rgba(180, 120, 30, 0.95) 100%);
	border-radius: 12px;
	padding: 0.6em 0.8em;
	box-shadow:
		0 8px 32px rgba(200, 140, 20, 0.4),
		0 2px 8px rgba(220, 160, 40, 0.3),
		inset 0 1px 0 rgba(255, 255, 255, 0.15),
		inset 0 -1px 0 rgba(160, 100, 10, 0.3);
	backdrop-filter: blur(10px) saturate(1.4);
	border: 1px solid rgba(240, 180, 60, 0.5);
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
}

.red-design::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg,
		rgba(255, 220, 100, 0.3) 0%,
		transparent 50%,
		rgba(255, 200, 80, 0.3) 100%);
	border-radius: 12px;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.red-design:hover {
	transform: translateY(-3px) scale(1.02);
	box-shadow:
		0 12px 40px rgba(200, 140, 20, 0.6),
		0 4px 12px rgba(220, 160, 40, 0.5),
		inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.red-design:hover::before {
	opacity: 1;
}

.text {
	padding: 0;
	text-align: center;
	margin: 0;
	color: rgb(255, 255, 255);
	font-family: 'Valo', Arial, Helvetica, sans-serif;
	text-shadow:
		0 2px 4px rgba(0, 0, 0, 0.8),
		0 0 8px rgba(255, 255, 255, 0.3);
	transition: all 0.3s ease;
	font-weight: 500;
	letter-spacing: 0.5px;
}

.name {
	padding-left: 1vh;
	padding-right: 1vh;
	font-size: 1.3em;
	font-weight: 600;
	background: linear-gradient(45deg,
		rgba(255, 255, 255, 0.1),
		rgba(255, 255, 255, 0.05));
	border-radius: 6px;
	margin: 0 2px;
	transition: all 0.3s ease;
}

.name:hover {
	background: linear-gradient(45deg,
		rgba(255, 255, 255, 0.15),
		rgba(255, 255, 255, 0.08));
	transform: scale(1.05);
}

.tag {
	padding-left: 1vh;
	padding-right: 0.5vh;
	margin-right: 0.2vh;
	font-size: 1.1em;
	font-weight: 700;
	background: linear-gradient(45deg,
		rgba(255, 255, 255, 0.2),
		rgba(255, 255, 255, 0.1));
	border-radius: 4px;
	border: 1px solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
}

.tag:hover {
	background: linear-gradient(45deg,
		rgba(255, 255, 255, 0.25),
		rgba(255, 255, 255, 0.15));
	transform: scale(1.1);
}

.message {
	padding-right: 0.85vh;
	font-size: 1.35em;
}

.dist {
	padding: 0.4vh 0.8vh;
	margin-right: 1vh;
	font-size: 0.9em;
	white-space: nowrap;
	font-weight: 600;
	background: linear-gradient(135deg,
		rgba(255, 255, 255, 0.15),
		rgba(255, 255, 255, 0.05));
	border: 2px solid rgba(255, 255, 255, 0.4);
	border-radius: 8px;
	backdrop-filter: blur(5px);
	transition: all 0.3s ease;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.dist:hover {
	background: linear-gradient(135deg,
		rgba(255, 255, 255, 0.25),
		rgba(255, 255, 255, 0.1));
	border-color: rgba(255, 255, 255, 0.6);
	transform: scale(1.1);
}

.none {
	padding: 0;
	padding-right: 0.85vh;
}

.weapon-image {
	height: 2.8vh;
	filter:
		drop-shadow(0 3px 6px rgba(0, 0, 0, 0.6))
		drop-shadow(0 0 8px rgba(255, 255, 255, 0.2));
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	margin: 0 4px;
	border-radius: 4px;
	background: rgba(255, 255, 255, 0.05);
	padding: 2px;
}

.weapon-image:hover {
	transform: scale(1.15) rotate(5deg);
	filter:
		drop-shadow(0 4px 8px rgba(0, 0, 0, 0.8))
		drop-shadow(0 0 12px rgba(255, 255, 255, 0.4));
	background: rgba(255, 255, 255, 0.1);
}

.icon-image {
	height: 2.8vh;
	padding: 2px;
	padding-left: 1vh;
	filter:
		drop-shadow(0 3px 6px rgba(0, 0, 0, 0.6))
		drop-shadow(0 0 8px rgba(255, 255, 255, 0.2));
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	border-radius: 4px;
	background: rgba(255, 255, 255, 0.05);
	margin: 0 2px;
}

.icon-image:hover {
	transform: scale(1.15) rotate(-5deg);
	filter:
		drop-shadow(0 4px 8px rgba(0, 0, 0, 0.8))
		drop-shadow(0 0 12px rgba(255, 255, 255, 0.4));
	background: rgba(255, 255, 255, 0.1);
}

/* Special effects for headshot icons */
.icon-image[alt="headshot"] {
	filter:
		drop-shadow(0 3px 6px rgba(255, 0, 0, 0.6))
		drop-shadow(0 0 8px rgba(255, 100, 100, 0.4));
}

.icon-image[alt="headshot"]:hover {
	filter:
		drop-shadow(0 4px 8px rgba(255, 0, 0, 0.8))
		drop-shadow(0 0 12px rgba(255, 100, 100, 0.6));
}

/* Special effects for noscope icons */
.icon-image[alt="noscoped"] {
	filter:
		drop-shadow(0 3px 6px rgba(0, 255, 255, 0.6))
		drop-shadow(0 0 8px rgba(100, 255, 255, 0.4));
}

.icon-image[alt="noscoped"]:hover {
	filter:
		drop-shadow(0 4px 8px rgba(0, 255, 255, 0.8))
		drop-shadow(0 0 12px rgba(100, 255, 255, 0.6));
}

/* ANIMATIONS  */

.animate__animated.animate__fadeInRight {
	--animate-duration: 400ms;
	animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate__animated.animate__flipOutX {
	--animate-duration: 0.7s;
	animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

/* Enhanced Pulse Animation for New Kills */
@keyframes killPulse {
	0% {
		transform: scale(1) translateX(0);
		box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
		filter: brightness(1);
	}
	25% {
		transform: scale(1.03) translateX(2px);
		box-shadow: 0 0 0 8px rgba(255, 255, 255, 0.3);
		filter: brightness(1.1);
	}
	50% {
		transform: scale(1.05) translateX(0);
		box-shadow: 0 0 0 15px rgba(255, 255, 255, 0.1);
		filter: brightness(1.2);
	}
	75% {
		transform: scale(1.02) translateX(-2px);
		box-shadow: 0 0 0 8px rgba(255, 255, 255, 0.2);
		filter: brightness(1.1);
	}
	100% {
		transform: scale(1) translateX(0);
		box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
		filter: brightness(1);
	}
}

.kill-pulse {
	animation: killPulse 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced Glow Effect for Special Kills */
@keyframes specialKillGlow {
	0%, 100% {
		filter: brightness(1) saturate(1) hue-rotate(0deg);
		box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
	}
	25% {
		filter: brightness(1.3) saturate(1.5) hue-rotate(10deg);
		box-shadow: 0 0 20px 5px rgba(255, 255, 255, 0.3);
	}
	50% {
		filter: brightness(1.5) saturate(2) hue-rotate(20deg);
		box-shadow: 0 0 30px 10px rgba(255, 255, 255, 0.4);
	}
	75% {
		filter: brightness(1.3) saturate(1.5) hue-rotate(10deg);
		box-shadow: 0 0 20px 5px rgba(255, 255, 255, 0.3);
	}
}

.special-kill {
	animation: specialKillGlow 1.2s ease-in-out;
}

/* Headshot Special Effect */
@keyframes headshotEffect {
	0%, 100% {
		filter: brightness(1) saturate(1);
		box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
	}
	50% {
		filter: brightness(1.4) saturate(1.8) hue-rotate(-10deg);
		box-shadow: 0 0 25px 8px rgba(255, 0, 0, 0.5);
	}
}

.headshot-kill {
	animation: headshotEffect 1s ease-in-out;
}

/* NoScope Special Effect */
@keyframes noscopeEffect {
	0%, 100% {
		filter: brightness(1) saturate(1);
		box-shadow: 0 0 0 0 rgba(0, 255, 255, 0);
	}
	50% {
		filter: brightness(1.4) saturate(1.8) hue-rotate(30deg);
		box-shadow: 0 0 25px 8px rgba(0, 255, 255, 0.5);
	}
}

.noscope-kill {
	animation: noscopeEffect 1s ease-in-out;
}

/* Floating Animation for Kill Elements */
@keyframes floatAnimation {
	0%, 100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-3px);
	}
}

.kill-container:hover {
	animation: floatAnimation 2s ease-in-out infinite;
}

/* COLOR SETTINGS MENU */

.settings-menu {
	position: fixed;
	top: 50%;
	right: 2vh;
	transform: translateY(-50%);
	width: 350px;
	max-height: 80vh;
	background: linear-gradient(135deg, rgba(30, 30, 30, 0.95), rgba(45, 45, 45, 0.95));
	border-radius: 15px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.1);
	z-index: 1000;
	transition: all 0.3s ease;
	overflow-y: auto;
}

.settings-menu.hidden {
	opacity: 0;
	visibility: hidden;
	transform: translateY(-50%) scale(0.9);
}

.settings-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-header h3 {
	color: #ffffff;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 1.4em;
	margin: 0;
	font-weight: 600;
}

.close-btn {
	background: none;
	border: none;
	color: #ffffff;
	font-size: 24px;
	cursor: pointer;
	padding: 5px;
	border-radius: 50%;
	transition: background-color 0.3s ease;
}

.close-btn:hover {
	background-color: rgba(255, 255, 255, 0.1);
}

.settings-content {
	padding: 20px;
}

.color-section {
	margin-bottom: 25px;
}

.color-section h4 {
	color: #ffffff;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 1.1em;
	margin: 0 0 15px 0;
	font-weight: 500;
	border-bottom: 1px solid rgba(255, 255, 255, 0.2);
	padding-bottom: 8px;
}

.color-option {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
	gap: 10px;
}

.color-option label {
	color: #ffffff;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.9em;
	min-width: 120px;
	font-weight: 400;
}

.color-option input[type="color"] {
	width: 40px;
	height: 30px;
	border: none;
	border-radius: 5px;
	cursor: pointer;
	background: none;
}

.color-option input[type="range"] {
	flex: 1;
	margin: 0 10px;
}

.opacity-label {
	color: #ffffff;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.8em;
	min-width: 35px;
	text-align: center;
}

.settings-buttons {
	display: flex;
	gap: 10px;
	margin-top: 20px;
}

.settings-buttons button {
	flex: 1;
	padding: 10px;
	border: none;
	border-radius: 8px;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.9em;
	cursor: pointer;
	transition: all 0.3s ease;
	font-weight: 500;
}

.apply-btn {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	color: white;
}

.apply-btn:hover {
	background: linear-gradient(135deg, #45a049, #3d8b40);
	transform: translateY(-2px);
}

.reset-btn {
	background: linear-gradient(135deg, #f44336, #da190b);
	color: white;
}

.reset-btn:hover {
	background: linear-gradient(135deg, #da190b, #c5150a);
	transform: translateY(-2px);
}

.save-btn {
	background: linear-gradient(135deg, #2196F3, #1976D2);
	color: white;
}

.save-btn:hover {
	background: linear-gradient(135deg, #1976D2, #1565C0);
	transform: translateY(-2px);
}

.settings-toggle {
	position: fixed;
	top: 20px;
	right: 20px;
	width: 50px;
	height: 50px;
	background: linear-gradient(135deg, rgba(30, 30, 30, 0.9), rgba(45, 45, 45, 0.9));
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	z-index: 999;
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-toggle:hover {
	transform: scale(1.1);
	background: linear-gradient(135deg, rgba(45, 45, 45, 0.9), rgba(60, 60, 60, 0.9));
}

.settings-toggle span {
	font-size: 20px;
}