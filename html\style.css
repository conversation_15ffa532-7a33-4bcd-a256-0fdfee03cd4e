/* Custom Font */

@font-face {
	font-family: Valo;
	src: url(valo.ttf);
}

/* Global Enhancements */
body {
	background: radial-gradient(ellipse at center,
		rgba(20, 20, 30, 0.1) 0%,
		rgba(10, 10, 20, 0.05) 70%,
		transparent 100%);
}

/* Scrollbar Styling */
#killfeed-container::-webkit-scrollbar {
	width: 4px;
}

#killfeed-container::-webkit-scrollbar-track {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 2px;
}

#killfeed-container::-webkit-scrollbar-thumb {
	background: linear-gradient(to bottom,
		rgba(255, 255, 255, 0.3),
		rgba(255, 255, 255, 0.1));
	border-radius: 2px;
}

#killfeed-container::-webkit-scrollbar-thumb:hover {
	background: linear-gradient(to bottom,
		rgba(255, 255, 255, 0.5),
		rgba(255, 255, 255, 0.2));
}

#killfeed-container {
	margin-top: 15%;
	margin-left: 2vh;
	width: 45vw;
	height: 65vh;
	float: left;
	overflow-y: hidden;
	overflow-x: hidden;
	position: relative;
	z-index: 10;
}

#killfeed-container::before {
	content: '';
	position: absolute;
	top: -10px;
	left: -10px;
	right: -10px;
	bottom: -10px;
	background: linear-gradient(45deg,
		rgba(255, 0, 150, 0.1) 0%,
		rgba(0, 255, 255, 0.1) 25%,
		rgba(255, 255, 0, 0.1) 50%,
		rgba(150, 0, 255, 0.1) 75%,
		rgba(255, 0, 150, 0.1) 100%);
	border-radius: 15px;
	filter: blur(15px);
	opacity: 0;
	animation: ambientGlow 8s ease-in-out infinite;
	z-index: -1;
}

@keyframes ambientGlow {
	0%, 100% { opacity: 0; }
	50% { opacity: 0.3; }
}

.kill-line {
	float: left;
	width: 100%;
	margin-bottom: 0.8vh;
	animation-fill-mode: forwards;
	position: relative;
	transform-origin: left center;
}

.kill-line::before {
	content: '';
	position: absolute;
	left: -5px;
	top: 50%;
	transform: translateY(-50%);
	width: 4px;
	height: 80%;
	background: linear-gradient(to bottom,
		rgba(255, 255, 255, 0.8),
		rgba(255, 255, 255, 0.2));
	border-radius: 2px;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.kill-line:hover::before {
	opacity: 1;
}

.kill-container {
	min-height: 3.5vh;
	float: left;
	display: inline-flex;
	align-items: center;
	justify-content: flex-start;
	position: relative;
	overflow: hidden;
}

.kill-container::after {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg,
		transparent,
		rgba(255, 255, 255, 0.2),
		transparent);
	transition: left 0.6s ease;
}

.kill-container:hover::after {
	left: 100%;
}

.line-clamp {
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;  
	overflow: hidden;
}

.black-design {
	background: linear-gradient(135deg,
		rgba(20, 20, 25, 0.95) 0%,
		rgba(35, 35, 45, 0.9) 50%,
		rgba(25, 25, 35, 0.95) 100%);
	border-radius: 12px;
	padding: 0.6em 0.8em;
	box-shadow:
		0 8px 32px rgba(0, 0, 0, 0.4),
		0 2px 8px rgba(0, 0, 0, 0.3),
		inset 0 1px 0 rgba(255, 255, 255, 0.1),
		inset 0 -1px 0 rgba(0, 0, 0, 0.2);
	backdrop-filter: blur(10px) saturate(1.2);
	border: 1px solid rgba(255, 255, 255, 0.08);
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
}

.black-design::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg,
		rgba(100, 100, 120, 0.1) 0%,
		transparent 50%,
		rgba(80, 80, 100, 0.1) 100%);
	border-radius: 12px;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.black-design:hover {
	transform: translateY(-3px) scale(1.02);
	box-shadow:
		0 12px 40px rgba(0, 0, 0, 0.5),
		0 4px 12px rgba(0, 0, 0, 0.4),
		inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.black-design:hover::before {
	opacity: 1;
}

.teal-design {
	background: linear-gradient(135deg,
		rgba(120, 20, 140, 0.95) 0%,
		rgba(160, 40, 180, 0.9) 30%,
		rgba(140, 60, 200, 0.9) 70%,
		rgba(110, 30, 160, 0.95) 100%);
	border-radius: 12px;
	padding: 0.6em 0.8em;
	box-shadow:
		0 8px 32px rgba(140, 30, 160, 0.4),
		0 2px 8px rgba(160, 40, 180, 0.3),
		inset 0 1px 0 rgba(255, 255, 255, 0.15),
		inset 0 -1px 0 rgba(100, 20, 120, 0.3);
	backdrop-filter: blur(10px) saturate(1.3);
	border: 1px solid rgba(180, 60, 200, 0.4);
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
}

.teal-design::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg,
		rgba(200, 100, 255, 0.2) 0%,
		transparent 50%,
		rgba(150, 80, 220, 0.2) 100%);
	border-radius: 12px;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.teal-design:hover {
	transform: translateY(-3px) scale(1.02);
	box-shadow:
		0 12px 40px rgba(140, 30, 160, 0.6),
		0 4px 12px rgba(160, 40, 180, 0.5),
		inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.teal-design:hover::before {
	opacity: 1;
}

.red-design {
	background: linear-gradient(135deg,
		rgba(200, 140, 20, 0.95) 0%,
		rgba(240, 180, 40, 0.9) 30%,
		rgba(220, 160, 60, 0.9) 70%,
		rgba(180, 120, 30, 0.95) 100%);
	border-radius: 12px;
	padding: 0.6em 0.8em;
	box-shadow:
		0 8px 32px rgba(200, 140, 20, 0.4),
		0 2px 8px rgba(220, 160, 40, 0.3),
		inset 0 1px 0 rgba(255, 255, 255, 0.15),
		inset 0 -1px 0 rgba(160, 100, 10, 0.3);
	backdrop-filter: blur(10px) saturate(1.4);
	border: 1px solid rgba(240, 180, 60, 0.5);
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
}

.red-design::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg,
		rgba(255, 220, 100, 0.3) 0%,
		transparent 50%,
		rgba(255, 200, 80, 0.3) 100%);
	border-radius: 12px;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.red-design:hover {
	transform: translateY(-3px) scale(1.02);
	box-shadow:
		0 12px 40px rgba(200, 140, 20, 0.6),
		0 4px 12px rgba(220, 160, 40, 0.5),
		inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.red-design:hover::before {
	opacity: 1;
}

.text {
	padding: 0;
	text-align: center;
	margin: 0;
	color: rgb(255, 255, 255);
	font-family: 'Valo', Arial, Helvetica, sans-serif;
	text-shadow:
		0 2px 4px rgba(0, 0, 0, 0.8),
		0 0 8px rgba(255, 255, 255, 0.3);
	transition: all 0.3s ease;
	font-weight: 500;
	letter-spacing: 0.5px;
}

.name {
	padding-left: 1vh;
	padding-right: 1vh;
	font-size: 1.3em;
	font-weight: 600;
	background: linear-gradient(45deg,
		rgba(255, 255, 255, 0.1),
		rgba(255, 255, 255, 0.05));
	border-radius: 6px;
	margin: 0 2px;
	transition: all 0.3s ease;
}

.name:hover {
	background: linear-gradient(45deg,
		rgba(255, 255, 255, 0.15),
		rgba(255, 255, 255, 0.08));
	transform: scale(1.05);
}

.tag {
	padding-left: 1vh;
	padding-right: 0.5vh;
	margin-right: 0.2vh;
	font-size: 1.1em;
	font-weight: 700;
	background: linear-gradient(45deg,
		rgba(255, 255, 255, 0.2),
		rgba(255, 255, 255, 0.1));
	border-radius: 4px;
	border: 1px solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
}

.tag:hover {
	background: linear-gradient(45deg,
		rgba(255, 255, 255, 0.25),
		rgba(255, 255, 255, 0.15));
	transform: scale(1.1);
}

.message {
	padding-right: 0.85vh;
	font-size: 1.35em;
}

.dist {
	padding: 0.5vh 1vh;
	margin-right: 1vh;
	margin-left: 0.5vh;
	font-size: 0.85em;
	white-space: nowrap;
	font-weight: 700;
	background: linear-gradient(135deg,
		rgba(0, 150, 255, 0.3),
		rgba(0, 100, 200, 0.2));
	border: 2px solid rgba(0, 150, 255, 0.6);
	border-radius: 12px;
	backdrop-filter: blur(8px);
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	text-shadow:
		0 1px 3px rgba(0, 0, 0, 0.8),
		0 0 8px rgba(0, 150, 255, 0.5);
	position: relative;
	overflow: hidden;
}

.dist::before {
	content: '📏';
	margin-right: 4px;
	font-size: 0.8em;
	opacity: 0.8;
}

.dist::after {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg,
		transparent,
		rgba(255, 255, 255, 0.3),
		transparent);
	transition: left 0.6s ease;
}

.dist:hover {
	background: linear-gradient(135deg,
		rgba(0, 150, 255, 0.5),
		rgba(0, 100, 200, 0.3));
	border-color: rgba(0, 200, 255, 0.8);
	transform: scale(1.15) rotate(2deg);
	box-shadow:
		0 4px 15px rgba(0, 150, 255, 0.4),
		inset 0 1px 0 rgba(255, 255, 255, 0.2);
	text-shadow:
		0 1px 3px rgba(0, 0, 0, 0.9),
		0 0 12px rgba(0, 150, 255, 0.8);
}

.dist:hover::after {
	left: 100%;
}

/* Special distance ranges with different colors */
.dist[data-range="close"] {
	background: linear-gradient(135deg,
		rgba(255, 100, 100, 0.3),
		rgba(200, 50, 50, 0.2));
	border-color: rgba(255, 100, 100, 0.6);
	text-shadow:
		0 1px 3px rgba(0, 0, 0, 0.8),
		0 0 8px rgba(255, 100, 100, 0.5);
}

.dist[data-range="medium"] {
	background: linear-gradient(135deg,
		rgba(255, 200, 0, 0.3),
		rgba(200, 150, 0, 0.2));
	border-color: rgba(255, 200, 0, 0.6);
	text-shadow:
		0 1px 3px rgba(0, 0, 0, 0.8),
		0 0 8px rgba(255, 200, 0, 0.5);
}

.dist[data-range="long"] {
	background: linear-gradient(135deg,
		rgba(100, 255, 100, 0.3),
		rgba(50, 200, 50, 0.2));
	border-color: rgba(100, 255, 100, 0.6);
	text-shadow:
		0 1px 3px rgba(0, 0, 0, 0.8),
		0 0 8px rgba(100, 255, 100, 0.5);
}

.none {
	padding: 0;
	padding-right: 0.85vh;
}

.weapon-image {
	height: 2.8vh;
	filter:
		drop-shadow(0 3px 6px rgba(0, 0, 0, 0.6))
		drop-shadow(0 0 8px rgba(255, 255, 255, 0.2));
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	margin: 0 4px;
	border-radius: 4px;
	background: rgba(255, 255, 255, 0.05);
	padding: 2px;
}

.weapon-image:hover {
	transform: scale(1.15) rotate(5deg);
	filter:
		drop-shadow(0 4px 8px rgba(0, 0, 0, 0.8))
		drop-shadow(0 0 12px rgba(255, 255, 255, 0.4));
	background: rgba(255, 255, 255, 0.1);
}

.icon-image {
	height: 2.8vh;
	padding: 2px;
	padding-left: 1vh;
	filter:
		drop-shadow(0 3px 6px rgba(0, 0, 0, 0.6))
		drop-shadow(0 0 8px rgba(255, 255, 255, 0.2));
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	border-radius: 4px;
	background: rgba(255, 255, 255, 0.05);
	margin: 0 2px;
}

.icon-image:hover {
	transform: scale(1.15) rotate(-5deg);
	filter:
		drop-shadow(0 4px 8px rgba(0, 0, 0, 0.8))
		drop-shadow(0 0 12px rgba(255, 255, 255, 0.4));
	background: rgba(255, 255, 255, 0.1);
}

/* Special effects for headshot icons */
.icon-image[alt="headshot"] {
	filter:
		drop-shadow(0 3px 6px rgba(255, 0, 0, 0.6))
		drop-shadow(0 0 8px rgba(255, 100, 100, 0.4));
}

.icon-image[alt="headshot"]:hover {
	filter:
		drop-shadow(0 4px 8px rgba(255, 0, 0, 0.8))
		drop-shadow(0 0 12px rgba(255, 100, 100, 0.6));
}

/* Special effects for noscope icons */
.icon-image[alt="noscoped"] {
	filter:
		drop-shadow(0 3px 6px rgba(0, 255, 255, 0.6))
		drop-shadow(0 0 8px rgba(100, 255, 255, 0.4));
}

.icon-image[alt="noscoped"]:hover {
	filter:
		drop-shadow(0 4px 8px rgba(0, 255, 255, 0.8))
		drop-shadow(0 0 12px rgba(100, 255, 255, 0.6));
}

/* ANIMATIONS  */

.animate__animated.animate__fadeInRight {
	--animate-duration: 400ms;
	animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate__animated.animate__flipOutX {
	--animate-duration: 0.7s;
	animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

/* Enhanced Pulse Animation for New Kills */
@keyframes killPulse {
	0% {
		transform: scale(1) translateX(0);
		box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
		filter: brightness(1);
	}
	25% {
		transform: scale(1.03) translateX(2px);
		box-shadow: 0 0 0 8px rgba(255, 255, 255, 0.3);
		filter: brightness(1.1);
	}
	50% {
		transform: scale(1.05) translateX(0);
		box-shadow: 0 0 0 15px rgba(255, 255, 255, 0.1);
		filter: brightness(1.2);
	}
	75% {
		transform: scale(1.02) translateX(-2px);
		box-shadow: 0 0 0 8px rgba(255, 255, 255, 0.2);
		filter: brightness(1.1);
	}
	100% {
		transform: scale(1) translateX(0);
		box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
		filter: brightness(1);
	}
}

.kill-pulse {
	animation: killPulse 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced Glow Effect for Special Kills */
@keyframes specialKillGlow {
	0%, 100% {
		filter: brightness(1) saturate(1) hue-rotate(0deg);
		box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
	}
	25% {
		filter: brightness(1.3) saturate(1.5) hue-rotate(10deg);
		box-shadow: 0 0 20px 5px rgba(255, 255, 255, 0.3);
	}
	50% {
		filter: brightness(1.5) saturate(2) hue-rotate(20deg);
		box-shadow: 0 0 30px 10px rgba(255, 255, 255, 0.4);
	}
	75% {
		filter: brightness(1.3) saturate(1.5) hue-rotate(10deg);
		box-shadow: 0 0 20px 5px rgba(255, 255, 255, 0.3);
	}
}

.special-kill {
	animation: specialKillGlow 1.2s ease-in-out;
}

/* Headshot Special Effect */
@keyframes headshotEffect {
	0%, 100% {
		filter: brightness(1) saturate(1);
		box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
	}
	50% {
		filter: brightness(1.4) saturate(1.8) hue-rotate(-10deg);
		box-shadow: 0 0 25px 8px rgba(255, 0, 0, 0.5);
	}
}

.headshot-kill {
	animation: headshotEffect 1s ease-in-out;
}

/* NoScope Special Effect */
@keyframes noscopeEffect {
	0%, 100% {
		filter: brightness(1) saturate(1);
		box-shadow: 0 0 0 0 rgba(0, 255, 255, 0);
	}
	50% {
		filter: brightness(1.4) saturate(1.8) hue-rotate(30deg);
		box-shadow: 0 0 25px 8px rgba(0, 255, 255, 0.5);
	}
}

.noscope-kill {
	animation: noscopeEffect 1s ease-in-out;
}

/* Floating Animation for Kill Elements */
@keyframes floatAnimation {
	0%, 100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-3px);
	}
}

.kill-container:hover {
	animation: floatAnimation 2s ease-in-out infinite;
}

/* ENHANCED COLOR SETTINGS MENU */

.settings-menu {
	position: fixed;
	top: 50%;
	right: 2vh;
	transform: translateY(-50%);
	width: 420px;
	max-height: 85vh;
	background: linear-gradient(135deg,
		rgba(15, 15, 25, 0.98) 0%,
		rgba(25, 25, 40, 0.95) 50%,
		rgba(20, 20, 35, 0.98) 100%);
	border-radius: 20px;
	box-shadow:
		0 20px 60px rgba(0, 0, 0, 0.6),
		0 5px 20px rgba(0, 0, 0, 0.4),
		inset 0 1px 0 rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(20px) saturate(1.2);
	border: 1px solid rgba(255, 255, 255, 0.15);
	z-index: 1000;
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	overflow: hidden;
}

.settings-menu.hidden {
	opacity: 0;
	visibility: hidden;
	transform: translateY(-50%) scale(0.8) rotateX(10deg);
}

.settings-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25px;
	background: linear-gradient(135deg,
		rgba(255, 255, 255, 0.05) 0%,
		rgba(255, 255, 255, 0.02) 100%);
	border-bottom: 1px solid rgba(255, 255, 255, 0.15);
	position: relative;
}

.settings-header::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(90deg,
		transparent 0%,
		rgba(255, 255, 255, 0.05) 50%,
		transparent 100%);
	animation: headerShimmer 3s ease-in-out infinite;
}

@keyframes headerShimmer {
	0%, 100% { opacity: 0; }
	50% { opacity: 1; }
}

.header-content {
	display: flex;
	align-items: center;
	gap: 15px;
}

.header-icon {
	font-size: 2em;
	background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.1); }
}

.header-text h3 {
	color: #ffffff;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 1.5em;
	margin: 0;
	font-weight: 700;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.header-subtitle {
	color: rgba(255, 255, 255, 0.7);
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.9em;
	margin: 2px 0 0 0;
	font-weight: 400;
}

.close-btn {
	background: linear-gradient(135deg, rgba(255, 0, 0, 0.2), rgba(200, 0, 0, 0.1));
	border: 1px solid rgba(255, 0, 0, 0.3);
	color: #ffffff;
	font-size: 20px;
	cursor: pointer;
	padding: 8px;
	border-radius: 50%;
	transition: all 0.3s ease;
	width: 36px;
	height: 36px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.close-btn:hover {
	background: linear-gradient(135deg, rgba(255, 0, 0, 0.4), rgba(200, 0, 0, 0.2));
	border-color: rgba(255, 0, 0, 0.6);
	transform: scale(1.1) rotate(90deg);
}

.settings-content {
	padding: 25px;
	max-height: calc(85vh - 120px);
	overflow-y: auto;
}

.settings-content::-webkit-scrollbar {
	width: 6px;
}

.settings-content::-webkit-scrollbar-track {
	background: rgba(255, 255, 255, 0.05);
	border-radius: 3px;
}

.settings-content::-webkit-scrollbar-thumb {
	background: linear-gradient(to bottom,
		rgba(255, 255, 255, 0.3),
		rgba(255, 255, 255, 0.1));
	border-radius: 3px;
}

.color-section {
	margin-bottom: 30px;
}

.section-header {
	margin-bottom: 20px;
}

.section-header h4 {
	color: #ffffff;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 1.2em;
	margin: 0 0 5px 0;
	font-weight: 600;
	display: flex;
	align-items: center;
	gap: 8px;
}

.section-desc {
	color: rgba(255, 255, 255, 0.6);
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.85em;
	margin: 0;
	font-weight: 400;
}

/* Color Cards */
.color-card {
	background: linear-gradient(135deg,
		rgba(255, 255, 255, 0.08) 0%,
		rgba(255, 255, 255, 0.03) 100%);
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 15px;
	padding: 20px;
	margin-bottom: 15px;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.color-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg,
		transparent,
		rgba(255, 255, 255, 0.1),
		transparent);
	transition: left 0.6s ease;
}

.color-card:hover {
	background: linear-gradient(135deg,
		rgba(255, 255, 255, 0.12) 0%,
		rgba(255, 255, 255, 0.06) 100%);
	border-color: rgba(255, 255, 255, 0.2);
	transform: translateY(-2px);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.color-card:hover::before {
	left: 100%;
}

.card-header {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-bottom: 15px;
}

.card-icon {
	font-size: 1.2em;
	width: 30px;
	text-align: center;
}

.card-title {
	color: #ffffff;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 1em;
	font-weight: 600;
	flex: 1;
}

.card-desc {
	color: rgba(255, 255, 255, 0.6);
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.8em;
	font-weight: 400;
}

.color-controls {
	display: flex;
	flex-direction: column;
	gap: 15px;
}

.color-input-wrapper {
	display: flex;
	align-items: center;
	gap: 15px;
}

.color-input-wrapper input[type="color"] {
	width: 50px;
	height: 35px;
	border: 2px solid rgba(255, 255, 255, 0.2);
	border-radius: 10px;
	cursor: pointer;
	background: none;
	transition: all 0.3s ease;
}

.color-input-wrapper input[type="color"]:hover {
	border-color: rgba(255, 255, 255, 0.4);
	transform: scale(1.05);
}

.color-input-wrapper label {
	color: rgba(255, 255, 255, 0.8);
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.9em;
	font-weight: 500;
}

.slider-wrapper {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.slider-wrapper input[type="range"] {
	width: 100%;
	height: 6px;
	border-radius: 3px;
	background: linear-gradient(to right,
		rgba(255, 255, 255, 0.2),
		rgba(255, 255, 255, 0.4));
	outline: none;
	cursor: pointer;
}

.slider-wrapper input[type="range"]::-webkit-slider-thumb {
	appearance: none;
	width: 18px;
	height: 18px;
	border-radius: 50%;
	background: linear-gradient(135deg, #4ecdc4, #44a08d);
	cursor: pointer;
	border: 2px solid rgba(255, 255, 255, 0.3);
	transition: all 0.3s ease;
}

.slider-wrapper input[type="range"]::-webkit-slider-thumb:hover {
	transform: scale(1.2);
	border-color: rgba(255, 255, 255, 0.6);
}

.slider-labels {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.slider-labels span {
	color: rgba(255, 255, 255, 0.7);
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.8em;
	font-weight: 500;
}

.opacity-label {
	background: linear-gradient(135deg,
		rgba(255, 255, 255, 0.15),
		rgba(255, 255, 255, 0.05));
	padding: 4px 8px;
	border-radius: 6px;
	border: 1px solid rgba(255, 255, 255, 0.2);
	font-weight: 600 !important;
}

.settings-buttons {
	display: flex;
	gap: 12px;
	margin-top: 25px;
	padding-top: 20px;
	border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-buttons button {
	flex: 1;
	padding: 12px 16px;
	border: none;
	border-radius: 12px;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.9em;
	cursor: pointer;
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;
	position: relative;
	overflow: hidden;
}

.settings-buttons button::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg,
		transparent,
		rgba(255, 255, 255, 0.2),
		transparent);
	transition: left 0.6s ease;
}

.settings-buttons button:hover::before {
	left: 100%;
}

.btn-icon {
	font-size: 1em;
}

.btn-text {
	font-weight: 600;
}

.apply-btn {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	color: white;
	border: 1px solid rgba(76, 175, 80, 0.3);
	box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.apply-btn:hover {
	background: linear-gradient(135deg, #45a049, #3d8b40);
	transform: translateY(-3px) scale(1.02);
	box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.reset-btn {
	background: linear-gradient(135deg, #f44336, #da190b);
	color: white;
	border: 1px solid rgba(244, 67, 54, 0.3);
	box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.reset-btn:hover {
	background: linear-gradient(135deg, #da190b, #c5150a);
	transform: translateY(-3px) scale(1.02);
	box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

.save-btn {
	background: linear-gradient(135deg, #2196F3, #1976D2);
	color: white;
	border: 1px solid rgba(33, 150, 243, 0.3);
	box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.save-btn:hover {
	background: linear-gradient(135deg, #1976D2, #1565C0);
	transform: translateY(-3px) scale(1.02);
	box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

.settings-toggle {
	position: fixed;
	top: 20px;
	right: 20px;
	width: 50px;
	height: 50px;
	background: linear-gradient(135deg, rgba(30, 30, 30, 0.9), rgba(45, 45, 45, 0.9));
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	z-index: 999;
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-toggle:hover {
	transform: scale(1.1);
	background: linear-gradient(135deg, rgba(45, 45, 45, 0.9), rgba(60, 60, 60, 0.9));
}

.settings-toggle span {
	font-size: 20px;
}

/* Debug Test Button */
.debug-test-btn {
	position: fixed;
	top: 80px;
	right: 20px;
	width: 60px;
	height: 40px;
	background: linear-gradient(135deg, rgba(255, 0, 0, 0.9), rgba(200, 0, 0, 0.9));
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	z-index: 999;
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.debug-test-btn:hover {
	transform: scale(1.1);
	background: linear-gradient(135deg, rgba(255, 50, 50, 0.9), rgba(220, 20, 20, 0.9));
}

.debug-test-btn span {
	color: white;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 12px;
	font-weight: bold;
}

/* Toggle Switch */
.toggle-wrapper {
	display: flex;
	align-items: center;
	gap: 15px;
	margin-bottom: 15px;
}

.toggle-switch {
	position: relative;
	display: inline-block;
	width: 50px;
	height: 24px;
}

.toggle-switch input {
	opacity: 0;
	width: 0;
	height: 0;
}

.toggle-slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
	border: 1px solid rgba(255, 255, 255, 0.3);
	transition: all 0.4s ease;
	border-radius: 24px;
}

.toggle-slider:before {
	position: absolute;
	content: "";
	height: 18px;
	width: 18px;
	left: 2px;
	bottom: 2px;
	background: linear-gradient(135deg, #ffffff, #f0f0f0);
	transition: all 0.4s ease;
	border-radius: 50%;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

input:checked + .toggle-slider {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	border-color: #4CAF50;
}

input:checked + .toggle-slider:before {
	transform: translateX(26px);
	background: linear-gradient(135deg, #ffffff, #e8f5e8);
}

.toggle-label {
	color: rgba(255, 255, 255, 0.9);
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.9em;
	font-weight: 500;
}

/* Image Upload */
.image-upload-wrapper {
	display: flex;
	flex-direction: column;
	gap: 15px;
}

.upload-area {
	border: 2px dashed rgba(255, 255, 255, 0.3);
	border-radius: 12px;
	padding: 30px 20px;
	text-align: center;
	cursor: pointer;
	transition: all 0.3s ease;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
}

.upload-area:hover {
	border-color: rgba(255, 255, 255, 0.5);
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
}

.upload-area.dragover {
	border-color: #4CAF50;
	background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
}

.upload-content {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.upload-icon {
	font-size: 2em;
}

.upload-text {
	color: rgba(255, 255, 255, 0.9);
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.9em;
	font-weight: 500;
}

.upload-formats {
	color: rgba(255, 255, 255, 0.6);
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.8em;
}

.image-preview {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 12px;
	overflow: hidden;
	background: rgba(255, 255, 255, 0.05);
	padding: 10px;
}

.image-preview img {
	max-width: 100px;
	max-height: 100px;
	border-radius: 8px;
	object-fit: cover;
}

.remove-image {
	position: absolute;
	top: 5px;
	right: 5px;
	background: rgba(255, 0, 0, 0.8);
	border: none;
	border-radius: 50%;
	width: 25px;
	height: 25px;
	cursor: pointer;
	font-size: 12px;
	color: white;
	transition: all 0.3s ease;
}

.remove-image:hover {
	background: rgba(255, 0, 0, 1);
	transform: scale(1.1);
}

.url-input-wrapper {
	display: flex;
	gap: 10px;
	align-items: center;
}

.url-input-wrapper input {
	flex: 1;
	padding: 10px;
	border: 1px solid rgba(255, 255, 255, 0.3);
	border-radius: 8px;
	background: rgba(255, 255, 255, 0.05);
	color: white;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.9em;
}

.url-input-wrapper input::placeholder {
	color: rgba(255, 255, 255, 0.5);
}

.load-btn {
	padding: 10px 15px;
	background: linear-gradient(135deg, #2196F3, #1976D2);
	border: none;
	border-radius: 8px;
	color: white;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.9em;
	cursor: pointer;
	transition: all 0.3s ease;
}

.load-btn:hover {
	background: linear-gradient(135deg, #1976D2, #1565C0);
	transform: translateY(-2px);
}

/* Row Separator Styles */
.row-separator {
	width: 100%;
	height: 2px;
	margin: 0.4vh 0;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	transition: all 0.3s ease;
	border-radius: 1px;
}

.row-separator:hover {
	transform: scaleY(1.5);
	filter: brightness(1.2);
}

.row-separator-line {
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg,
		transparent 0%,
		currentColor 20%,
		currentColor 80%,
		transparent 100%);
	border-radius: 1px;
}

.row-separator-icon {
	height: 1.5vh;
	width: auto;
	max-width: 3vh;
	object-fit: contain;
	filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
	transition: all 0.3s ease;
}

.row-separator-icon:hover {
	transform: scale(1.2);
	filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.7));
}

/* Notification Animations */
@keyframes slideIn {
	from {
		transform: translateX(100%);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}

@keyframes slideOut {
	from {
		transform: translateX(0);
		opacity: 1;
	}
	to {
		transform: translateX(100%);
		opacity: 0;
	}
}