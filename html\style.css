/* Custom Font */

@font-face {
	font-family: Valo;
	src: url(valo.ttf);
}

#killfeed-container {
	margin-top: 17%;
	margin-left: 1vh;
	width: 49vw;
	height: 60vh;
	float: left;
	overflow-y: hidden;
	overflow-x: hidden;
}

.kill-line {
	float: left;
	width: 100%;
	margin-bottom: 0.35vh;
	animation-fill-mode: forwards;
}

.kill-container {
	height: 1vh;
	float: left;
	display:inline-flex;
	align-items: center;
	justify-content: center;
}

.line-clamp {
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;  
	overflow: hidden;
}

.black-design {
	background: linear-gradient(135deg, rgba(30, 30, 30, 0.288), rgba(45, 45, 45, 0.288));
	border-radius: 0.8em;
	padding: 0.4em 0.5em 0.4em 0.5em;
	box-shadow: rgba(42, 42, 42, 0.692) 0px 4px 12px, inset 0 1px 0 rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(5px);
	border: 1px solid rgba(255, 255, 255, 0.05);
	transition: all 0.3s ease;
}

.black-design:hover {
	transform: translateY(-2px);
	box-shadow: rgba(42, 42, 42, 0.8) 0px 6px 16px, inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.teal-design {
	background: linear-gradient(135deg, rgba(143, 16, 155, 0.295), rgba(180, 50, 200, 0.295));
	border-radius: 0.8em;
	padding: 0.4em 0.5em 0.4em 0.5em;
	box-shadow: rgba(197, 30, 189, 0.664) 0px 4px 12px, inset 0 1px 0 rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(5px);
	border: 1px solid rgba(197, 30, 189, 0.3);
	transition: all 0.3s ease;
}

.teal-design:hover {
	transform: translateY(-2px);
	box-shadow: rgba(197, 30, 189, 0.8) 0px 6px 16px, inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.red-design {
	background: linear-gradient(135deg, rgba(190, 165, 25, 0.247), rgba(220, 190, 50, 0.247));
	border-radius: 0.8em;
	padding: 0.4em 0.5em 0.4em 0.5em;
	box-shadow: rgba(190, 165, 25, 0.699) 0px 4px 12px, inset 0 1px 0 rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(5px);
	border: 1px solid rgba(190, 165, 25, 0.4);
	transition: all 0.3s ease;
}

.red-design:hover {
	transform: translateY(-2px);
	box-shadow: rgba(190, 165, 25, 0.8) 0px 6px 16px, inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.text {
	padding: 0;
	text-align: center;
	margin: 0;
	color: rgb(255, 255, 255);
	font-family: 'Valo', Arial, Helvetica, sans-serif;
	text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
	transition: all 0.3s ease;
}

.name {
	padding-left: 0.85vh;
	padding-right: 0.85vh;
	font-size: 1.2em;
	font-weight: 500;
}

.tag {
	padding-left: 0.85vh;
	margin-right: -0.30vh;
	font-size: 1.20em;
}

.message {
	padding-right: 0.85vh;
	font-size: 1.35em;
}

.dist {
	padding-left: 0.4vh;
	padding-right: 0.4vh;
	padding-top: 0.2vh;
	padding-bottom: 0.2vh;
	margin-right: 0.82vh;
	font-size: 1.0em;
	white-space: nowrap;
	font-weight: 100;
	border: solid rgb(255, 255, 255) 2px;
	border-radius: 5px;
}

.none {
	padding: 0;
	padding-right: 0.85vh;
}

.weapon-image {
	height: 2.3vh;
	filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
	transition: all 0.3s ease;
}

.weapon-image:hover {
	transform: scale(1.1);
	filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.7));
}

.icon-image {
	height: 2.6vh;
	padding: 0;
	padding-left: 0.85vh;
	filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
	transition: all 0.3s ease;
}

.icon-image:hover {
	transform: scale(1.1);
	filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.7));
}

/* ANIMATIONS  */

.animate__animated.animate__fadeInRight {
	--animate-duration: 400ms;
	animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate__animated.animate__flipOutX {
	--animate-duration: 0.7s;
	animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

/* Custom Pulse Animation for New Kills */
@keyframes killPulse {
	0% {
		transform: scale(1);
		box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
	}
	50% {
		transform: scale(1.02);
		box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
	}
	100% {
		transform: scale(1);
		box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
	}
}

.kill-pulse {
	animation: killPulse 0.6s ease-out;
}

/* Glow Effect for Special Kills */
@keyframes glowEffect {
	0%, 100% {
		filter: brightness(1) saturate(1);
	}
	50% {
		filter: brightness(1.2) saturate(1.3);
	}
}

.special-kill {
	animation: glowEffect 1s ease-in-out;
}

/* COLOR SETTINGS MENU */

.settings-menu {
	position: fixed;
	top: 50%;
	right: 2vh;
	transform: translateY(-50%);
	width: 350px;
	max-height: 80vh;
	background: linear-gradient(135deg, rgba(30, 30, 30, 0.95), rgba(45, 45, 45, 0.95));
	border-radius: 15px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.1);
	z-index: 1000;
	transition: all 0.3s ease;
	overflow-y: auto;
}

.settings-menu.hidden {
	opacity: 0;
	visibility: hidden;
	transform: translateY(-50%) scale(0.9);
}

.settings-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-header h3 {
	color: #ffffff;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 1.4em;
	margin: 0;
	font-weight: 600;
}

.close-btn {
	background: none;
	border: none;
	color: #ffffff;
	font-size: 24px;
	cursor: pointer;
	padding: 5px;
	border-radius: 50%;
	transition: background-color 0.3s ease;
}

.close-btn:hover {
	background-color: rgba(255, 255, 255, 0.1);
}

.settings-content {
	padding: 20px;
}

.color-section {
	margin-bottom: 25px;
}

.color-section h4 {
	color: #ffffff;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 1.1em;
	margin: 0 0 15px 0;
	font-weight: 500;
	border-bottom: 1px solid rgba(255, 255, 255, 0.2);
	padding-bottom: 8px;
}

.color-option {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
	gap: 10px;
}

.color-option label {
	color: #ffffff;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.9em;
	min-width: 120px;
	font-weight: 400;
}

.color-option input[type="color"] {
	width: 40px;
	height: 30px;
	border: none;
	border-radius: 5px;
	cursor: pointer;
	background: none;
}

.color-option input[type="range"] {
	flex: 1;
	margin: 0 10px;
}

.opacity-label {
	color: #ffffff;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.8em;
	min-width: 35px;
	text-align: center;
}

.settings-buttons {
	display: flex;
	gap: 10px;
	margin-top: 20px;
}

.settings-buttons button {
	flex: 1;
	padding: 10px;
	border: none;
	border-radius: 8px;
	font-family: 'Valo', Arial, sans-serif;
	font-size: 0.9em;
	cursor: pointer;
	transition: all 0.3s ease;
	font-weight: 500;
}

.apply-btn {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	color: white;
}

.apply-btn:hover {
	background: linear-gradient(135deg, #45a049, #3d8b40);
	transform: translateY(-2px);
}

.reset-btn {
	background: linear-gradient(135deg, #f44336, #da190b);
	color: white;
}

.reset-btn:hover {
	background: linear-gradient(135deg, #da190b, #c5150a);
	transform: translateY(-2px);
}

.save-btn {
	background: linear-gradient(135deg, #2196F3, #1976D2);
	color: white;
}

.save-btn:hover {
	background: linear-gradient(135deg, #1976D2, #1565C0);
	transform: translateY(-2px);
}

.settings-toggle {
	position: fixed;
	top: 20px;
	right: 20px;
	width: 50px;
	height: 50px;
	background: linear-gradient(135deg, rgba(30, 30, 30, 0.9), rgba(45, 45, 45, 0.9));
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	z-index: 999;
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-toggle:hover {
	transform: scale(1.1);
	background: linear-gradient(135deg, rgba(45, 45, 45, 0.9), rgba(60, 60, 60, 0.9));
}

.settings-toggle span {
	font-size: 20px;
}