let showTime = 7500;
let maxLines = 8;
let textColor = "255, 255, 255";

// Color settings
let colorSettings = {
	blackBg: { color: "#1e1e1e", opacity: 29 },
	tealBg: { color: "#8f109b", opacity: 30 },
	redBg: { color: "#bea519", opacity: 25 },
	textColor: "#ffffff",
	shadowOpacity: 69,
	rowSeparators: { enabled: true }, // تفعيل الفواصل
	rowColor: { color: "#000000", opacity: 50 }, // لون الفاصل
	customRowIcon: { enabled: false, url: "" } // صورة مخصصة للفاصل
};

window.onload = () => {
	window.addEventListener('message', onEventRecieved);
	initializeColorSettings();
};

function AppendToFeed(id, string) {
	if ($("#killfeed-container").children().length >= maxLines) {
		$("#killfeed-container").children().first().remove();
	};

	// Add row separator if enabled and there are existing items
	if (colorSettings && colorSettings.rowSeparators && colorSettings.rowSeparators.enabled && $("#killfeed-container").children().length > 0) {
		try {
			const separatorId = `separator_${id}`;
			const separatorString = createRowSeparator(separatorId);
			$("#killfeed-container").append(separatorString);
		} catch (error) {
			console.log('Error creating separator:', error);
		}
	}

	$("#killfeed-container").append(string);

	let lineContainer = $('.kill-container[data-id=' + id + ']');
	let killLine = $('.kill-line[data-line-id=' + id + ']');

	// Add pulse effect for new kills
	lineContainer.addClass('kill-pulse');

	// Add special visual effects based on kill type
	setTimeout(() => {
		if (headshot) {
			lineContainer.addClass('headshot-kill');
		}
		if (noScoped) {
			lineContainer.addClass('noscope-kill');
		}
	}, 200);

	lineContainer.hide().fadeIn(200).delay(showTime+600).fadeOut();
	setTimeout(function(){
		killLine.addClass("animate__animated");
		killLine.addClass("animate__flipOutX");
		setTimeout(function(){
			killLine.remove();
		}, 700); // Updated timing
	}, showTime);  // time it lasts
};

function addKill(data) {
	const id = data.id;
	const image = data.image;
	const design = data.design;
	const noScoped = data.noScoped;
	const headshot = data.headshot;
	const driveBy = data.driveBy;
	const dist = data.dist;

	let victim = data.victim;
	let killer = data.killer;
	
	let duplicate = $(`.kill-line[data-line-id=${id}]`);
	if (duplicate.length > 0) {
		duplicate.remove();
	};

	// Check for special kills and add appropriate classes
	let specialClasses = [];
	if (headshot) specialClasses.push('headshot-kill');
	if (noScoped) specialClasses.push('noscope-kill');
	if (driveBy !== false && driveBy !== undefined) specialClasses.push('special-kill');

	let specialClass = specialClasses.length > 0 ? ' ' + specialClasses.join(' ') : '';

	// string creation
	let appendString = `<div data-line-id="${id}" class="kill-line animate__animated animate__fadeInRight"><div data-id="${id}" class="kill-container ${design}${specialClass}">`;
    
	if (killer.name != undefined) {
		if (killer.tag != undefined) {
			appendString = appendString + `<p class="text tag" style="color: rgb(${textColor});">${killer.tag}</p>`;
		};
		appendString = appendString + `<p class="text line-clamp name" style="color: rgb(${textColor});">${killer.name}</p>`;
	} else {
		appendString = appendString + '<p class="none"></p>';
	};
	
	if (image != undefined) {
		appendString = appendString + `<img src="images/${image}.png" alt="${image}" class="weapon-image">`;
	};

	if (noScoped == true) {
		appendString = appendString + '<img src="images/noscoped.png" alt="noscoped" class="icon-image">';
	};

	if (driveBy != false && driveBy != undefined) {
		appendString = appendString + `<img src="images/${driveBy}.png" alt="driveBy" class="icon-image">`;
	};

	if (headshot == true) {
		appendString = appendString + '<img src="images/headshot.png" alt="headshot" class="icon-image">';
	};

	if (victim.name != undefined) {
		if (victim.tag != undefined) {
			appendString = appendString + `<p class="text tag" style="color: rgb(${textColor});">${victim.tag}</p>`;
		};
		appendString = appendString + `<p class="text line-clamp name" style="color: rgb(${textColor});">${victim.name}</p>`;

		if (dist != false && dist != undefined) {
			// Extract distance value and determine range
			let distanceRange = getDistanceRange(dist);
			appendString = appendString + `<p class="text dist" data-range="${distanceRange}" style="color: rgb(${textColor});">${dist}</p>`;
		};
	};

	appendString = appendString + '</div></div>';
	
	AppendToFeed(id, appendString)
};

function onEventRecieved(info) {
	let event = info.data;
	console.log('Event received:', event);

	if (event.data == undefined && event.action !== "toggleColorSettings") {
		console.error("event.data was nil!");
		return;
	};

	if (event.action == "addKill") {
		addKill(event.data);
	} else if (event.action == "toggleKillfeed") {
		if (event.data.state == true) {
			$("#killfeed-container").show()
		} else {
			$("#killfeed-container").hide()
		}
	} else if (event.action == "setConfig") {
		showTime = event.data.showTime;
		maxLines = event.data.maxLines;
	} else if (event.action == "toggleColorSettings") {
		console.log('toggleColorSettings action received');
		toggleColorSettings();
	} else if (event.action == "closeColorSettings") {
		console.log('closeColorSettings action received');
		hideColorSettings();
	} else if (event.action == "restartKillfeed") {
		console.log('restartKillfeed action received');
		restartKillfeed();
	} else if (event.action == "debug") {
		console.log('Debug message received:', event.data);
	} else {
		console.log("event.action was not specified:", event.action);
	}
};

// Color Settings Functions
function initializeColorSettings() {
	// Load saved settings from localStorage
	const saved = localStorage.getItem('killfeed-colors');
	if (saved) {
		colorSettings = JSON.parse(saved);
		updateColorInputs();
	}

	// Initialize event listeners with error checking
	const settingsToggle = document.getElementById('settings-toggle');
	if (settingsToggle) {
		settingsToggle.addEventListener('click', toggleColorSettings);
	}

	const closeSettings = document.getElementById('close-settings');
	if (closeSettings) {
		closeSettings.addEventListener('click', hideColorSettings);
	}

	const applyBtn = document.getElementById('apply-colors');
	if (applyBtn) {
		applyBtn.addEventListener('click', applyColors);
	}

	const resetBtn = document.getElementById('reset-colors');
	if (resetBtn) {
		resetBtn.addEventListener('click', resetColors);
	}

	const saveBtn = document.getElementById('save-colors');
	if (saveBtn) {
		saveBtn.addEventListener('click', saveColors);
	}

	// Opacity sliders with error checking
	const blackOpacity = document.getElementById('black-opacity');
	if (blackOpacity) {
		blackOpacity.addEventListener('input', updateOpacityLabel);
	}

	const tealOpacity = document.getElementById('teal-opacity');
	if (tealOpacity) {
		tealOpacity.addEventListener('input', updateOpacityLabel);
	}

	const redOpacity = document.getElementById('red-opacity');
	if (redOpacity) {
		redOpacity.addEventListener('input', updateOpacityLabel);
	}

	const shadowOpacity = document.getElementById('shadow-opacity');
	if (shadowOpacity) {
		shadowOpacity.addEventListener('input', updateOpacityLabel);
	}

	const rowOpacity = document.getElementById('row-opacity');
	if (rowOpacity) {
		rowOpacity.addEventListener('input', updateOpacityLabel);
	}

	const rowSeparatorsEnabled = document.getElementById('row-separators-enabled');
	if (rowSeparatorsEnabled) {
		rowSeparatorsEnabled.addEventListener('change', function() {
			colorSettings.rowSeparators.enabled = this.checked;
		});
	}

	// Custom icon functionality
	initializeCustomIcon();

	// Apply initial colors
	applyColors();

	// Add debug test button listener
	const debugBtn = document.getElementById('debug-test-btn');
	if (debugBtn) {
		debugBtn.addEventListener('click', function() {
			console.log('Debug button clicked - toggling color settings');
			toggleColorSettings();
		});
	}

	// Add quick control buttons
	const closeMenuBtn = document.getElementById('close-menu-btn');
	if (closeMenuBtn) {
		closeMenuBtn.addEventListener('click', function() {
			console.log('Quick close button clicked');
			hideColorSettings();
		});
	}

	const restartBtn = document.getElementById('restart-killfeed-btn');
	if (restartBtn) {
		restartBtn.addEventListener('click', function() {
			console.log('Quick restart button clicked');
			restartKillfeed();
		});
	}

	// Add keyboard listeners for menu control
	document.addEventListener('keydown', function(event) {
		const menu = document.getElementById('color-settings-menu');
		if (menu && !menu.classList.contains('hidden')) {
			// Close menu with ESC or O key
			if (event.key === 'Escape' || event.key === 'o' || event.key === 'O') {
				event.preventDefault();
				hideColorSettings();
			}
		}
	});

	// Add click outside to close
	document.addEventListener('click', function(event) {
		const menu = document.getElementById('color-settings-menu');
		const settingsToggle = document.getElementById('settings-toggle');
		const debugBtn = document.getElementById('debug-test-btn');

		if (menu && !menu.classList.contains('hidden')) {
			// Check if click is outside menu and not on toggle buttons
			if (!menu.contains(event.target) &&
				!settingsToggle.contains(event.target) &&
				(!debugBtn || !debugBtn.contains(event.target))) {
				hideColorSettings();
			}
		}
	});

	console.log('Color settings initialized successfully');
}

function toggleColorSettings() {
	console.log('toggleColorSettings called');
	const menu = document.getElementById('color-settings-menu');
	if (menu) {
		const isHidden = menu.classList.contains('hidden');
		menu.classList.toggle('hidden');

		// Enable/disable cursor and NUI focus
		if (isHidden) {
			// Opening menu - enable cursor
			fetch(`https://${GetParentResourceName()}/enableCursor`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json; charset=UTF-8' },
				body: JSON.stringify({ enable: true })
			});
		} else {
			// Closing menu - disable cursor
			fetch(`https://${GetParentResourceName()}/enableCursor`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json; charset=UTF-8' },
				body: JSON.stringify({ enable: false })
			});
		}

		console.log('Menu toggled, hidden class:', menu.classList.contains('hidden'));
	} else {
		console.error('color-settings-menu element not found!');
	}
}

function showPermissionDenied() {
	// Create temporary notification
	const notification = document.createElement('div');
	notification.style.cssText = `
		position: fixed;
		top: 20px;
		right: 20px;
		background: linear-gradient(135deg, rgba(255, 0, 0, 0.9), rgba(200, 0, 0, 0.9));
		color: white;
		padding: 15px 20px;
		border-radius: 10px;
		font-family: 'Valo', Arial, sans-serif;
		font-size: 14px;
		z-index: 10000;
		backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.2);
		animation: slideIn 0.3s ease-out;
	`;
	notification.textContent = 'ليس لديك صلاحية لاستخدام قائمة الألوان';
	document.body.appendChild(notification);

	// Remove after 3 seconds
	setTimeout(() => {
		notification.style.animation = 'slideOut 0.3s ease-in';
		setTimeout(() => {
			if (notification.parentNode) {
				notification.parentNode.removeChild(notification);
			}
		}, 300);
	}, 3000);
}

function hideColorSettings() {
	const menu = document.getElementById('color-settings-menu');
	if (menu) {
		menu.classList.add('hidden');

		// Disable cursor when closing
		fetch(`https://${GetParentResourceName()}/enableCursor`, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json; charset=UTF-8' },
			body: JSON.stringify({ enable: false })
		});

		console.log('Menu closed');
	}
}

function restartKillfeed() {
	console.log('Restarting killfeed...');

	// Clear all existing kill entries
	$("#killfeed-container").empty();

	// Reset color settings to defaults if they're corrupted
	if (!colorSettings || typeof colorSettings !== 'object') {
		colorSettings = {
			blackBg: { color: "#1e1e1e", opacity: 29 },
			tealBg: { color: "#8f109b", opacity: 30 },
			redBg: { color: "#bea519", opacity: 25 },
			textColor: "#ffffff",
			shadowOpacity: 69,
			rowSeparators: { enabled: true },
			rowColor: { color: "#000000", opacity: 50 },
			customRowIcon: { enabled: false, url: "" }
		};
	}

	// Re-initialize color settings
	initializeColorSettings();

	// Show killfeed container
	$("#killfeed-container").show();

	console.log('Killfeed restarted successfully');
}

function updateOpacityLabel(event) {
	const slider = event.target;
	const labelsContainer = slider.parentElement.querySelector('.slider-labels');
	const label = labelsContainer.querySelector('.opacity-label');
	label.textContent = slider.value + '%';
}

function updateColorInputs() {
	const blackBgColor = document.getElementById('black-bg-color');
	if (blackBgColor) blackBgColor.value = colorSettings.blackBg.color;

	const blackOpacity = document.getElementById('black-opacity');
	if (blackOpacity) blackOpacity.value = colorSettings.blackBg.opacity;

	const tealBgColor = document.getElementById('teal-bg-color');
	if (tealBgColor) tealBgColor.value = colorSettings.tealBg.color;

	const tealOpacity = document.getElementById('teal-opacity');
	if (tealOpacity) tealOpacity.value = colorSettings.tealBg.opacity;

	const redBgColor = document.getElementById('red-bg-color');
	if (redBgColor) redBgColor.value = colorSettings.redBg.color;

	const redOpacity = document.getElementById('red-opacity');
	if (redOpacity) redOpacity.value = colorSettings.redBg.opacity;

	const textColor = document.getElementById('text-color');
	if (textColor) textColor.value = colorSettings.textColor;

	const shadowOpacity = document.getElementById('shadow-opacity');
	if (shadowOpacity) shadowOpacity.value = colorSettings.shadowOpacity;

	const rowColor = document.getElementById('row-color');
	if (rowColor) rowColor.value = colorSettings.rowColor.color;

	const rowOpacity = document.getElementById('row-opacity');
	if (rowOpacity) rowOpacity.value = colorSettings.rowColor.opacity;

	const rowSeparatorsEnabled = document.getElementById('row-separators-enabled');
	if (rowSeparatorsEnabled) rowSeparatorsEnabled.checked = colorSettings.rowSeparators.enabled;

	const customIconEnabled = document.getElementById('custom-icon-enabled');
	if (customIconEnabled) customIconEnabled.checked = colorSettings.customRowIcon.enabled;

	const customIconUrl = document.getElementById('custom-icon-url');
	if (customIconUrl) customIconUrl.value = colorSettings.customRowIcon.url;

	// Update opacity labels with new structure
	try {
		const blackLabel = document.querySelector('#black-opacity').parentElement.querySelector('.opacity-label');
		if (blackLabel) blackLabel.textContent = colorSettings.blackBg.opacity + '%';

		const tealLabel = document.querySelector('#teal-opacity').parentElement.querySelector('.opacity-label');
		if (tealLabel) tealLabel.textContent = colorSettings.tealBg.opacity + '%';

		const redLabel = document.querySelector('#red-opacity').parentElement.querySelector('.opacity-label');
		if (redLabel) redLabel.textContent = colorSettings.redBg.opacity + '%';

		const shadowLabel = document.querySelector('#shadow-opacity').parentElement.querySelector('.opacity-label');
		if (shadowLabel) shadowLabel.textContent = colorSettings.shadowOpacity + '%';

		const rowLabel = document.querySelector('#row-opacity').parentElement.querySelector('.opacity-label');
		if (rowLabel) rowLabel.textContent = colorSettings.rowColor.opacity + '%';
	} catch (error) {
		console.log('Error updating opacity labels:', error);
	}
}

function applyColors() {
	// Get values from inputs
	const blackBgColorEl = document.getElementById('black-bg-color');
	if (blackBgColorEl) colorSettings.blackBg.color = blackBgColorEl.value;

	const blackOpacityEl = document.getElementById('black-opacity');
	if (blackOpacityEl) colorSettings.blackBg.opacity = parseInt(blackOpacityEl.value);

	const tealBgColorEl = document.getElementById('teal-bg-color');
	if (tealBgColorEl) colorSettings.tealBg.color = tealBgColorEl.value;

	const tealOpacityEl = document.getElementById('teal-opacity');
	if (tealOpacityEl) colorSettings.tealBg.opacity = parseInt(tealOpacityEl.value);

	const redBgColorEl = document.getElementById('red-bg-color');
	if (redBgColorEl) colorSettings.redBg.color = redBgColorEl.value;

	const redOpacityEl = document.getElementById('red-opacity');
	if (redOpacityEl) colorSettings.redBg.opacity = parseInt(redOpacityEl.value);

	const textColorEl = document.getElementById('text-color');
	if (textColorEl) colorSettings.textColor = textColorEl.value;

	const shadowOpacityEl = document.getElementById('shadow-opacity');
	if (shadowOpacityEl) colorSettings.shadowOpacity = parseInt(shadowOpacityEl.value);

	const rowColorEl = document.getElementById('row-color');
	if (rowColorEl) colorSettings.rowColor.color = rowColorEl.value;

	const rowOpacityEl = document.getElementById('row-opacity');
	if (rowOpacityEl) colorSettings.rowColor.opacity = parseInt(rowOpacityEl.value);

	const rowSeparatorsEnabledEl = document.getElementById('row-separators-enabled');
	if (rowSeparatorsEnabledEl) colorSettings.rowSeparators.enabled = rowSeparatorsEnabledEl.checked;

	const customIconEnabledEl = document.getElementById('custom-icon-enabled');
	if (customIconEnabledEl) colorSettings.customRowIcon.enabled = customIconEnabledEl.checked;

	const customIconUrlEl = document.getElementById('custom-icon-url');
	if (customIconUrlEl) colorSettings.customRowIcon.url = customIconUrlEl.value;

	// Convert hex to RGB
	const blackRgb = hexToRgb(colorSettings.blackBg.color);
	const tealRgb = hexToRgb(colorSettings.tealBg.color);
	const redRgb = hexToRgb(colorSettings.redBg.color);
	const textRgb = hexToRgb(colorSettings.textColor);
	const rowRgb = hexToRgb(colorSettings.rowColor.color);

	// Update CSS variables
	const root = document.documentElement;
	root.style.setProperty('--black-bg', `rgba(${blackRgb.r}, ${blackRgb.g}, ${blackRgb.b}, ${colorSettings.blackBg.opacity / 100})`);
	root.style.setProperty('--teal-bg', `rgba(${tealRgb.r}, ${tealRgb.g}, ${tealRgb.b}, ${colorSettings.tealBg.opacity / 100})`);
	root.style.setProperty('--red-bg', `rgba(${redRgb.r}, ${redRgb.g}, ${redRgb.b}, ${colorSettings.redBg.opacity / 100})`);
	root.style.setProperty('--text-color', `rgb(${textRgb.r}, ${textRgb.g}, ${textRgb.b})`);
	root.style.setProperty('--shadow-opacity', colorSettings.shadowOpacity / 100);
	root.style.setProperty('--row-bg', `rgba(${rowRgb.r}, ${rowRgb.g}, ${rowRgb.b}, ${colorSettings.rowColor.opacity / 100})`);

	// Update global text color variable
	textColor = `${textRgb.r}, ${textRgb.g}, ${textRgb.b}`;

	// Apply styles dynamically
	updateDynamicStyles();
}

function updateDynamicStyles() {
	// Remove existing dynamic styles
	const existingStyle = document.getElementById('dynamic-colors');
	if (existingStyle) {
		existingStyle.remove();
	}

	// Create new style element
	const style = document.createElement('style');
	style.id = 'dynamic-colors';

	const blackRgb = hexToRgb(colorSettings.blackBg.color);
	const tealRgb = hexToRgb(colorSettings.tealBg.color);
	const redRgb = hexToRgb(colorSettings.redBg.color);
	const rowRgb = hexToRgb(colorSettings.rowColor.color);

	style.textContent = `
		.black-design {
			background-color: rgba(${blackRgb.r}, ${blackRgb.g}, ${blackRgb.b}, ${colorSettings.blackBg.opacity / 100}) !important;
			box-shadow: rgba(${blackRgb.r}, ${blackRgb.g}, ${blackRgb.b}, ${colorSettings.shadowOpacity / 100}) 0px 4px 12px !important;
		}
		.teal-design {
			background-color: rgba(${tealRgb.r}, ${tealRgb.g}, ${tealRgb.b}, ${colorSettings.tealBg.opacity / 100}) !important;
			box-shadow: rgba(${tealRgb.r}, ${tealRgb.g}, ${tealRgb.b}, ${colorSettings.shadowOpacity / 100}) 0px 4px 12px !important;
		}
		.red-design {
			background-color: rgba(${redRgb.r}, ${redRgb.g}, ${redRgb.b}, ${colorSettings.redBg.opacity / 100}) !important;
			box-shadow: rgba(${redRgb.r}, ${redRgb.g}, ${redRgb.b}, ${colorSettings.shadowOpacity / 100}) 0px 4px 12px !important;
		}
		.row-separator {
			background-color: rgba(${rowRgb.r}, ${rowRgb.g}, ${rowRgb.b}, ${colorSettings.rowColor.opacity / 100}) !important;
			color: rgba(${rowRgb.r}, ${rowRgb.g}, ${rowRgb.b}, ${Math.min(colorSettings.rowColor.opacity / 100 + 0.3, 1)}) !important;
		}
	`;

	document.head.appendChild(style);
}

function resetColors() {
	colorSettings = {
		blackBg: { color: "#1e1e1e", opacity: 29 },
		tealBg: { color: "#8f109b", opacity: 30 },
		redBg: { color: "#bea519", opacity: 25 },
		textColor: "#ffffff",
		shadowOpacity: 69,
		rowSeparators: { enabled: true },
		rowColor: { color: "#000000", opacity: 50 },
		customRowIcon: { enabled: false, url: "" }
	};

	// Reset image preview
	const imagePreview = document.getElementById('image-preview');
	if (imagePreview) imagePreview.style.display = 'none';

	const uploadSection = document.getElementById('image-upload-section');
	if (uploadSection) uploadSection.style.display = 'none';

	updateColorInputs();
	applyColors();
}

function saveColors() {
	localStorage.setItem('killfeed-colors', JSON.stringify(colorSettings));

	// Show save confirmation
	const saveBtn = document.getElementById('save-colors');
	const originalText = saveBtn.textContent;
	saveBtn.textContent = 'تم الحفظ!';
	saveBtn.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';

	setTimeout(() => {
		saveBtn.textContent = originalText;
		saveBtn.style.background = 'linear-gradient(135deg, #2196F3, #1976D2)';
	}, 2000);
}

function hexToRgb(hex) {
	const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
	return result ? {
		r: parseInt(result[1], 16),
		g: parseInt(result[2], 16),
		b: parseInt(result[3], 16)
	} : null;
}

function getDistanceRange(distString) {
	// Extract numeric value from distance string (e.g., "150 m" -> 150)
	const match = distString.match(/(\d+)/);
	if (!match) return 'medium';

	const distance = parseInt(match[1]);

	if (distance <= 25) {
		return 'close';  // Red - Close range
	} else if (distance <= 100) {
		return 'medium'; // Yellow - Medium range
	} else {
		return 'long';   // Green - Long range
	}
}

// Custom Icon Functions
function initializeCustomIcon() {
	const customIconEnabled = document.getElementById('custom-icon-enabled');
	const uploadArea = document.getElementById('upload-area');
	const fileInput = document.getElementById('custom-icon-file');
	const imagePreview = document.getElementById('image-preview');
	const previewImg = document.getElementById('preview-img');
	const removeImage = document.getElementById('remove-image');
	const urlInput = document.getElementById('custom-icon-url');
	const loadUrlBtn = document.getElementById('load-url-image');
	const uploadSection = document.getElementById('image-upload-section');

	if (!customIconEnabled) return;

	// Toggle upload section visibility
	customIconEnabled.addEventListener('change', function() {
		if (this.checked) {
			uploadSection.style.display = 'flex';
		} else {
			uploadSection.style.display = 'none';
			colorSettings.customRowIcon.enabled = false;
			colorSettings.customRowIcon.url = '';
			imagePreview.style.display = 'none';
		}
	});

	// File upload handling
	uploadArea.addEventListener('click', () => fileInput.click());

	uploadArea.addEventListener('dragover', (e) => {
		e.preventDefault();
		uploadArea.classList.add('dragover');
	});

	uploadArea.addEventListener('dragleave', () => {
		uploadArea.classList.remove('dragover');
	});

	uploadArea.addEventListener('drop', (e) => {
		e.preventDefault();
		uploadArea.classList.remove('dragover');
		const files = e.dataTransfer.files;
		if (files.length > 0) {
			handleImageFile(files[0]);
		}
	});

	fileInput.addEventListener('change', (e) => {
		if (e.target.files.length > 0) {
			handleImageFile(e.target.files[0]);
		}
	});

	// URL loading
	loadUrlBtn.addEventListener('click', () => {
		const url = urlInput.value.trim();
		if (url) {
			loadImageFromUrl(url);
		}
	});

	// Remove image
	removeImage.addEventListener('click', () => {
		imagePreview.style.display = 'none';
		colorSettings.customRowIcon.enabled = false;
		colorSettings.customRowIcon.url = '';
		urlInput.value = '';
		fileInput.value = '';
	});

	// Initialize state
	if (colorSettings.customRowIcon.enabled) {
		customIconEnabled.checked = true;
		uploadSection.style.display = 'flex';
		if (colorSettings.customRowIcon.url) {
			previewImg.src = colorSettings.customRowIcon.url;
			imagePreview.style.display = 'flex';
		}
	} else {
		uploadSection.style.display = 'none';
	}
}

function handleImageFile(file) {
	// Check file size (2MB limit)
	if (file.size > 2 * 1024 * 1024) {
		alert('حجم الملف كبير جداً. الحد الأقصى 2MB');
		return;
	}

	// Check file type
	if (!file.type.startsWith('image/')) {
		alert('يرجى اختيار ملف صورة صحيح');
		return;
	}

	const reader = new FileReader();
	reader.onload = function(e) {
		const imageUrl = e.target.result;
		setCustomIcon(imageUrl);
	};
	reader.readAsDataURL(file);
}

function loadImageFromUrl(url) {
	const img = new Image();
	img.onload = function() {
		setCustomIcon(url);
	};
	img.onerror = function() {
		alert('فشل في تحميل الصورة. تأكد من صحة الرابط');
	};
	img.src = url;
}

function setCustomIcon(imageUrl) {
	const previewImg = document.getElementById('preview-img');
	const imagePreview = document.getElementById('image-preview');

	previewImg.src = imageUrl;
	imagePreview.style.display = 'flex';

	colorSettings.customRowIcon.enabled = true;
	colorSettings.customRowIcon.url = imageUrl;

	console.log('Custom icon set:', imageUrl);
}

// Row Separator Functions
function createRowSeparator(id) {
	try {
		if (!colorSettings || !colorSettings.rowColor) {
			return '<div class="row-separator"><div class="row-separator-line"></div></div>';
		}

		const rowRgb = hexToRgb(colorSettings.rowColor.color) || { r: 0, g: 0, b: 0 };
		const opacity = (colorSettings.rowColor.opacity || 50) / 100;

		let separatorContent = '';

		if (colorSettings.customRowIcon && colorSettings.customRowIcon.enabled && colorSettings.customRowIcon.url) {
			// Use custom image
			separatorContent = `<img src="${colorSettings.customRowIcon.url}" alt="separator" class="row-separator-icon">`;
		} else {
			// Use default line
			separatorContent = '<div class="row-separator-line"></div>';
		}

		return `
			<div data-separator-id="${id}" class="row-separator" style="background-color: rgba(${rowRgb.r}, ${rowRgb.g}, ${rowRgb.b}, ${opacity});">
				${separatorContent}
			</div>
		`;
	} catch (error) {
		console.log('Error in createRowSeparator:', error);
		return '<div class="row-separator"><div class="row-separator-line"></div></div>';
	}
}
