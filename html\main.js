let showTime = 7500;
let maxLines = 8;
let textColor = "255, 255, 255";

// Color settings
let colorSettings = {
	blackBg: { color: "#1e1e1e", opacity: 29 },
	tealBg: { color: "#8f109b", opacity: 30 },
	redBg: { color: "#bea519", opacity: 25 },
	textColor: "#ffffff",
	shadowOpacity: 69
};

window.onload = (e) => {
	window.addEventListener('message', onEventRecieved);
	initializeColorSettings();
};

function AppendToFeed(id, string) {
	if ($("#killfeed-container").children().length >= maxLines) {
		$("#killfeed-container").children().first().remove();
	};

	$("#killfeed-container").append(string);

	let lineContainer = $('.kill-container[data-id=' + id + ']');
	let killLine = $('.kill-line[data-line-id=' + id + ']');

	// Add pulse effect for new kills
	lineContainer.addClass('kill-pulse');

	lineContainer.hide().fadeIn(150).delay(showTime+600).fadeOut();
	setTimeout(function(){
		killLine.addClass("animate__animated");
		killLine.addClass("animate__flipOutX");
		setTimeout(function(){
			killLine.remove();
		}, 700); // Updated timing
	}, showTime);  // time it lasts
};

function addKill(data) {
	const id = data.id;
	const image = data.image;
	const design = data.design;
	const noScoped = data.noScoped;
	const headshot = data.headshot;
	const driveBy = data.driveBy;
	const dist = data.dist;

	let victim = data.victim;
	let killer = data.killer;
	
	let duplicate = $(`.kill-line[data-line-id=${id}]`);
	if (duplicate.length > 0) {
		duplicate.remove();
	};

	// Check for special kills
	let isSpecialKill = headshot || noScoped || (driveBy !== false && driveBy !== undefined);
	let specialClass = isSpecialKill ? ' special-kill' : '';

	// string creation
	let appendString = `<div data-line-id="${id}" class="kill-line animate__animated animate__fadeInRight"><div data-id="${id}" class="kill-container ${design}${specialClass}">`;
    
	if (killer.name != undefined) {
		if (killer.tag != undefined) {
			appendString = appendString + `<p class="text tag" style="color: rgb(${textColor});">${killer.tag}</p>`;
		};
		appendString = appendString + `<p class="text line-clamp name" style="color: rgb(${textColor});">${killer.name}</p>`;
	} else {
		appendString = appendString + '<p class="none"></p>';
	};
	
	if (image != undefined) {
		appendString = appendString + `<img src="images/${image}.png" alt="${image}" class="weapon-image">`;
	};

	if (noScoped == true) {
		appendString = appendString + '<img src="images/noscoped.png" alt="noscoped" class="icon-image">';
	};

	if (driveBy != false && driveBy != undefined) {
		appendString = appendString + `<img src="images/${driveBy}.png" alt="driveBy" class="icon-image">`;
	};

	if (headshot == true) {
		appendString = appendString + '<img src="images/headshot.png" alt="headshot" class="icon-image">';
	};

	if (victim.name != undefined) {
		if (victim.tag != undefined) {
			appendString = appendString + `<p class="text tag" style="color: rgb(${textColor});">${victim.tag}</p>`;
		};
		appendString = appendString + `<p class="text line-clamp name" style="color: rgb(${textColor});">${victim.name}</p>`;

		if (dist != false && dist != undefined) {
			appendString = appendString + `<p class="text dist" style="color: rgb(${textColor});">${dist}</p>`;
		};
	};

	appendString = appendString + '</div></div>';
	
	AppendToFeed(id, appendString)
};

function onEventRecieved(info) {
	let event = info.data;
	if (event.data == undefined) {
		console.error("event.data was nil!");
		return;
	};

	if (event.action == "addKill") {
		addKill(event.data);
	} else if (event.action == "toggleKillfeed") {
		if (event.data.state == true) {
			$("#killfeed-container").show()
		} else {
			$("#killfeed-container").hide()
		}
	} else if (event.action == "setConfig") {
		showTime = event.data.showTime;
		maxLines = event.data.maxLines;
	} else if (event.action == "toggleColorSettings") {
		toggleColorSettings();
	} else {
		console.log("event.action was not specified");
	}
};

// Color Settings Functions
function initializeColorSettings() {
	// Load saved settings from localStorage
	const saved = localStorage.getItem('killfeed-colors');
	if (saved) {
		colorSettings = JSON.parse(saved);
		updateColorInputs();
	}

	// Initialize event listeners
	document.getElementById('settings-toggle').addEventListener('click', toggleColorSettings);
	document.getElementById('close-settings').addEventListener('click', hideColorSettings);
	document.getElementById('apply-colors').addEventListener('click', applyColors);
	document.getElementById('reset-colors').addEventListener('click', resetColors);
	document.getElementById('save-colors').addEventListener('click', saveColors);

	// Opacity sliders
	document.getElementById('black-opacity').addEventListener('input', updateOpacityLabel);
	document.getElementById('teal-opacity').addEventListener('input', updateOpacityLabel);
	document.getElementById('red-opacity').addEventListener('input', updateOpacityLabel);
	document.getElementById('shadow-opacity').addEventListener('input', updateOpacityLabel);

	// Apply initial colors
	applyColors();
}

function toggleColorSettings() {
	const menu = document.getElementById('color-settings-menu');
	menu.classList.toggle('hidden');
}

function hideColorSettings() {
	const menu = document.getElementById('color-settings-menu');
	menu.classList.add('hidden');
}

function updateOpacityLabel(event) {
	const slider = event.target;
	const label = slider.nextElementSibling;
	label.textContent = slider.value + '%';
}

function updateColorInputs() {
	document.getElementById('black-bg-color').value = colorSettings.blackBg.color;
	document.getElementById('black-opacity').value = colorSettings.blackBg.opacity;
	document.getElementById('teal-bg-color').value = colorSettings.tealBg.color;
	document.getElementById('teal-opacity').value = colorSettings.tealBg.opacity;
	document.getElementById('red-bg-color').value = colorSettings.redBg.color;
	document.getElementById('red-opacity').value = colorSettings.redBg.opacity;
	document.getElementById('text-color').value = colorSettings.textColor;
	document.getElementById('shadow-opacity').value = colorSettings.shadowOpacity;

	// Update opacity labels
	document.querySelector('#black-opacity + .opacity-label').textContent = colorSettings.blackBg.opacity + '%';
	document.querySelector('#teal-opacity + .opacity-label').textContent = colorSettings.tealBg.opacity + '%';
	document.querySelector('#red-opacity + .opacity-label').textContent = colorSettings.redBg.opacity + '%';
	document.querySelector('#shadow-opacity + .opacity-label').textContent = colorSettings.shadowOpacity + '%';
}

function applyColors() {
	// Get values from inputs
	colorSettings.blackBg.color = document.getElementById('black-bg-color').value;
	colorSettings.blackBg.opacity = parseInt(document.getElementById('black-opacity').value);
	colorSettings.tealBg.color = document.getElementById('teal-bg-color').value;
	colorSettings.tealBg.opacity = parseInt(document.getElementById('teal-opacity').value);
	colorSettings.redBg.color = document.getElementById('red-bg-color').value;
	colorSettings.redBg.opacity = parseInt(document.getElementById('red-opacity').value);
	colorSettings.textColor = document.getElementById('text-color').value;
	colorSettings.shadowOpacity = parseInt(document.getElementById('shadow-opacity').value);

	// Convert hex to RGB
	const blackRgb = hexToRgb(colorSettings.blackBg.color);
	const tealRgb = hexToRgb(colorSettings.tealBg.color);
	const redRgb = hexToRgb(colorSettings.redBg.color);
	const textRgb = hexToRgb(colorSettings.textColor);

	// Update CSS variables
	const root = document.documentElement;
	root.style.setProperty('--black-bg', `rgba(${blackRgb.r}, ${blackRgb.g}, ${blackRgb.b}, ${colorSettings.blackBg.opacity / 100})`);
	root.style.setProperty('--teal-bg', `rgba(${tealRgb.r}, ${tealRgb.g}, ${tealRgb.b}, ${colorSettings.tealBg.opacity / 100})`);
	root.style.setProperty('--red-bg', `rgba(${redRgb.r}, ${redRgb.g}, ${redRgb.b}, ${colorSettings.redBg.opacity / 100})`);
	root.style.setProperty('--text-color', `rgb(${textRgb.r}, ${textRgb.g}, ${textRgb.b})`);
	root.style.setProperty('--shadow-opacity', colorSettings.shadowOpacity / 100);

	// Update global text color variable
	textColor = `${textRgb.r}, ${textRgb.g}, ${textRgb.b}`;

	// Apply styles dynamically
	updateDynamicStyles();
}

function updateDynamicStyles() {
	// Remove existing dynamic styles
	const existingStyle = document.getElementById('dynamic-colors');
	if (existingStyle) {
		existingStyle.remove();
	}

	// Create new style element
	const style = document.createElement('style');
	style.id = 'dynamic-colors';

	const blackRgb = hexToRgb(colorSettings.blackBg.color);
	const tealRgb = hexToRgb(colorSettings.tealBg.color);
	const redRgb = hexToRgb(colorSettings.redBg.color);

	style.textContent = `
		.black-design {
			background-color: rgba(${blackRgb.r}, ${blackRgb.g}, ${blackRgb.b}, ${colorSettings.blackBg.opacity / 100}) !important;
			box-shadow: rgba(${blackRgb.r}, ${blackRgb.g}, ${blackRgb.b}, ${colorSettings.shadowOpacity / 100}) 0px 4px 12px !important;
		}
		.teal-design {
			background-color: rgba(${tealRgb.r}, ${tealRgb.g}, ${tealRgb.b}, ${colorSettings.tealBg.opacity / 100}) !important;
			box-shadow: rgba(${tealRgb.r}, ${tealRgb.g}, ${tealRgb.b}, ${colorSettings.shadowOpacity / 100}) 0px 4px 12px !important;
		}
		.red-design {
			background-color: rgba(${redRgb.r}, ${redRgb.g}, ${redRgb.b}, ${colorSettings.redBg.opacity / 100}) !important;
			box-shadow: rgba(${redRgb.r}, ${redRgb.g}, ${redRgb.b}, ${colorSettings.shadowOpacity / 100}) 0px 4px 12px !important;
		}
	`;

	document.head.appendChild(style);
}

function resetColors() {
	colorSettings = {
		blackBg: { color: "#1e1e1e", opacity: 29 },
		tealBg: { color: "#8f109b", opacity: 30 },
		redBg: { color: "#bea519", opacity: 25 },
		textColor: "#ffffff",
		shadowOpacity: 69
	};

	updateColorInputs();
	applyColors();
}

function saveColors() {
	localStorage.setItem('killfeed-colors', JSON.stringify(colorSettings));

	// Show save confirmation
	const saveBtn = document.getElementById('save-colors');
	const originalText = saveBtn.textContent;
	saveBtn.textContent = 'تم الحفظ!';
	saveBtn.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';

	setTimeout(() => {
		saveBtn.textContent = originalText;
		saveBtn.style.background = 'linear-gradient(135deg, #2196F3, #1976D2)';
	}, 2000);
}

function hexToRgb(hex) {
	const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
	return result ? {
		r: parseInt(result[1], 16),
		g: parseInt(result[2], 16),
		b: parseInt(result[3], 16)
	} : null;
}
