<!DOCTYPE html>
<html>
	<head>
		
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width,initial-scale=1.0"/>
		<title>CB Killfeed</title>
		<link rel="stylesheet" href="style.css"/>
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
		<script src="nui://game/ui/jquery.js" type="text/javascript"></script>
		<script src="./main.js" type="text/javascript"></script>
	</head>
	<body>
		<div id="killfeed-container"></div>

		<!-- Enhanced Color Settings Menu -->
		<div id="color-settings-menu" class="settings-menu hidden">
			<div class="settings-header">
				<div class="header-content">
					<div class="header-icon">🎨</div>
					<div class="header-text">
						<h3>إعدادات الألوان</h3>
						<p class="header-subtitle">تخصيص مظهر الـ Killfeed</p>
					</div>
				</div>
				<button id="close-settings" class="close-btn">×</button>
			</div>

			<div class="settings-content">
				<!-- Background Colors Section -->
				<div class="color-section">
					<div class="section-header">
						<h4>🎭 ألوان الخلفية</h4>
						<p class="section-desc">تخصيص ألوان خلفية أنواع القتل المختلفة</p>
					</div>

					<div class="color-card">
						<div class="card-header">
							<span class="card-icon">⚫</span>
							<span class="card-title">التصميم الأسود</span>
							<span class="card-desc">القتلات العادية</span>
						</div>
						<div class="color-controls">
							<div class="color-input-wrapper">
								<input type="color" id="black-bg-color" value="#1e1e1e">
								<label for="black-bg-color">اللون</label>
							</div>
							<div class="slider-wrapper">
								<input type="range" id="black-opacity" min="0" max="100" value="29">
								<div class="slider-labels">
									<span>الشفافية</span>
									<span class="opacity-label">29%</span>
								</div>
							</div>
						</div>
					</div>

					<div class="color-card">
						<div class="card-header">
							<span class="card-icon">🟣</span>
							<span class="card-title">التصميم البنفسجي</span>
							<span class="card-desc">عندما تقتل أنت</span>
						</div>
						<div class="color-controls">
							<div class="color-input-wrapper">
								<input type="color" id="teal-bg-color" value="#8f109b">
								<label for="teal-bg-color">اللون</label>
							</div>
							<div class="slider-wrapper">
								<input type="range" id="teal-opacity" min="0" max="100" value="30">
								<div class="slider-labels">
									<span>الشفافية</span>
									<span class="opacity-label">30%</span>
								</div>
							</div>
						</div>
					</div>

					<div class="color-card">
						<div class="card-header">
							<span class="card-icon">🟡</span>
							<span class="card-title">التصميم الذهبي</span>
							<span class="card-desc">عندما يتم قتلك</span>
						</div>
						<div class="color-controls">
							<div class="color-input-wrapper">
								<input type="color" id="red-bg-color" value="#bea519">
								<label for="red-bg-color">اللون</label>
							</div>
							<div class="slider-wrapper">
								<input type="range" id="red-opacity" min="0" max="100" value="25">
								<div class="slider-labels">
									<span>الشفافية</span>
									<span class="opacity-label">25%</span>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Text Settings Section -->
				<div class="color-section">
					<div class="section-header">
						<h4>📝 إعدادات النص</h4>
						<p class="section-desc">تخصيص مظهر النصوص والكتابة</p>
					</div>

					<div class="color-card">
						<div class="card-header">
							<span class="card-icon">🔤</span>
							<span class="card-title">لون النص الرئيسي</span>
							<span class="card-desc">لون جميع النصوص</span>
						</div>
						<div class="color-controls">
							<div class="color-input-wrapper">
								<input type="color" id="text-color" value="#ffffff">
								<label for="text-color">اللون</label>
							</div>
						</div>
					</div>
				</div>

				<!-- Row Styling Section -->
				<div class="color-section">
					<div class="section-header">
						<h4>📋 إعدادات الصفوف</h4>
						<p class="section-desc">تخصيص مظهر صفوف الـ killfeed</p>
					</div>

					<div class="color-card">
						<div class="card-header">
							<span class="card-icon">⬛</span>
							<span class="card-title">فواصل الصفوف</span>
							<span class="card-desc">خطوط فاصلة بين القتلات</span>
						</div>
						<div class="color-controls">
							<div class="toggle-wrapper">
								<label class="toggle-switch">
									<input type="checkbox" id="row-separators-enabled" checked>
									<span class="toggle-slider"></span>
								</label>
								<span class="toggle-label">تفعيل الفواصل</span>
							</div>
							<div class="color-input-wrapper">
								<input type="color" id="row-color" value="#000000">
								<label for="row-color">لون الفاصل</label>
							</div>
							<div class="slider-wrapper">
								<input type="range" id="row-opacity" min="0" max="100" value="50">
								<div class="slider-labels">
									<span>الشفافية</span>
									<span class="opacity-label">50%</span>
								</div>
							</div>
						</div>
					</div>

					<div class="color-card">
						<div class="card-header">
							<span class="card-icon">🖼️</span>
							<span class="card-title">صورة مخصصة للصفر</span>
							<span class="card-desc">استبدال أيقونة الصفر بصورة</span>
						</div>
						<div class="color-controls">
							<div class="toggle-wrapper">
								<label class="toggle-switch">
									<input type="checkbox" id="custom-icon-enabled">
									<span class="toggle-slider"></span>
								</label>
								<span class="toggle-label">تفعيل الصورة المخصصة</span>
							</div>
							<div class="image-upload-wrapper" id="image-upload-section">
								<div class="upload-area" id="upload-area">
									<div class="upload-content">
										<span class="upload-icon">📁</span>
										<span class="upload-text">اضغط لرفع صورة أو اسحبها هنا</span>
										<span class="upload-formats">PNG, JPG, GIF (حد أقصى 2MB)</span>
									</div>
									<input type="file" id="custom-icon-file" accept="image/*" style="display: none;">
								</div>
								<div class="image-preview" id="image-preview" style="display: none;">
									<img id="preview-img" src="" alt="معاينة">
									<button class="remove-image" id="remove-image">🗑️</button>
								</div>
								<div class="url-input-wrapper">
									<input type="url" id="custom-icon-url" placeholder="أو أدخل رابط الصورة مباشرة">
									<button id="load-url-image" class="load-btn">تحميل</button>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Advanced Settings Section -->
				<div class="color-section">
					<div class="section-header">
						<h4>⚙️ إعدادات متقدمة</h4>
						<p class="section-desc">تحكم في التأثيرات البصرية</p>
					</div>

					<div class="color-card">
						<div class="card-header">
							<span class="card-icon">🌟</span>
							<span class="card-title">شفافية الظل</span>
							<span class="card-desc">قوة الظلال والتأثيرات</span>
						</div>
						<div class="color-controls">
							<div class="slider-wrapper">
								<input type="range" id="shadow-opacity" min="0" max="100" value="69">
								<div class="slider-labels">
									<span>الشفافية</span>
									<span class="opacity-label">69%</span>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Action Buttons -->
				<div class="settings-buttons">
					<button id="apply-colors" class="apply-btn">
						<span class="btn-icon">✅</span>
						<span class="btn-text">تطبيق</span>
					</button>
					<button id="reset-colors" class="reset-btn">
						<span class="btn-icon">🔄</span>
						<span class="btn-text">إعادة تعيين</span>
					</button>
					<button id="save-colors" class="save-btn">
						<span class="btn-icon">💾</span>
						<span class="btn-text">حفظ</span>
					</button>
				</div>
			</div>
		</div>

		<!-- Settings Toggle Button -->
		<div id="settings-toggle" class="settings-toggle">
			<span>🎨</span>
		</div>

		<!-- Debug Test Button -->
		<div id="debug-test-btn" class="debug-test-btn">
			<span>TEST</span>
		</div>

		<!-- Quick Control Buttons -->
		<div id="quick-controls" class="quick-controls">
			<button id="close-menu-btn" class="quick-btn close-btn-quick">ESC</button>
			<button id="restart-killfeed-btn" class="quick-btn restart-btn">🔄</button>
		</div>
	</body>
</html>