<!DOCTYPE html>
<html>
	<head>
		
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width,initial-scale=1.0"/>
		<title>CB Killfeed</title>
		<link rel="stylesheet" href="style.css"/>
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
		<script src="nui://game/ui/jquery.js" type="text/javascript"></script>
		<script src="./main.js" type="text/javascript"></script>
	</head>
	<body>
		<div id="killfeed-container"></div>

		<!-- Color Settings Menu -->
		<div id="color-settings-menu" class="settings-menu hidden">
			<div class="settings-header">
				<h3>إعدادات الألوان</h3>
				<button id="close-settings" class="close-btn">×</button>
			</div>
			<div class="settings-content">
				<div class="color-section">
					<h4>ألوان الخلفية</h4>
					<div class="color-option">
						<label>التصميم الأسود:</label>
						<input type="color" id="black-bg-color" value="#1e1e1e">
						<input type="range" id="black-opacity" min="0" max="100" value="29">
						<span class="opacity-label">29%</span>
					</div>
					<div class="color-option">
						<label>التصميم البنفسجي:</label>
						<input type="color" id="teal-bg-color" value="#8f109b">
						<input type="range" id="teal-opacity" min="0" max="100" value="30">
						<span class="opacity-label">30%</span>
					</div>
					<div class="color-option">
						<label>التصميم الذهبي:</label>
						<input type="color" id="red-bg-color" value="#bea519">
						<input type="range" id="red-opacity" min="0" max="100" value="25">
						<span class="opacity-label">25%</span>
					</div>
				</div>

				<div class="color-section">
					<h4>لون النص</h4>
					<div class="color-option">
						<label>لون النص الرئيسي:</label>
						<input type="color" id="text-color" value="#ffffff">
					</div>
				</div>

				<div class="color-section">
					<h4>إعدادات إضافية</h4>
					<div class="color-option">
						<label>شفافية الظل:</label>
						<input type="range" id="shadow-opacity" min="0" max="100" value="69">
						<span class="opacity-label">69%</span>
					</div>
				</div>

				<div class="settings-buttons">
					<button id="apply-colors" class="apply-btn">تطبيق</button>
					<button id="reset-colors" class="reset-btn">إعادة تعيين</button>
					<button id="save-colors" class="save-btn">حفظ</button>
				</div>
			</div>
		</div>

		<!-- Settings Toggle Button -->
		<div id="settings-toggle" class="settings-toggle">
			<span>🎨</span>
		</div>
	</body>
</html>