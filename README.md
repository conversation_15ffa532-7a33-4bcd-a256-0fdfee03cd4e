# CB Killfeed - Enhanced Version

## الميزات الجديدة

### 🎨 قائمة إعدادات الألوان
- إمكانية تخصيص ألوان الخلفية لكل نوع من أنواع القتل
- تعديل شفافية الألوان
- تغيير لون النص
- حفظ الإعدادات محلياً

### 📍 الموقع الجديد
- تم نقل الـ killfeed إلى الجانب الأيسر من الشاشة
- تصميم محسن مع تأثيرات بصرية

### ✨ التحسينات البصرية
- تأثيرات انتقالية سلسة
- ظلال وتأثيرات ضوئية
- تأثيرات خاصة للقتلات المميزة (headshot, noscope, driveby)
- تأثير نبضة للقتلات الجديدة

## كيفية الاستخدام

### فتح قائمة الألوان
يمكنك فتح قائمة إعدادات الألوان بعدة طرق:

1. **الضغط على F9** (الافتراضي)
2. **كتابة الأمر:** `/killfeedcolors`
3. **الضغط على أيقونة الإعدادات** 🎨 في أعلى يمين الشاشة

### إعدادات الألوان المتاحة

#### ألوان الخلفية:
- **التصميم الأسود:** للقتلات العادية
- **التصميم البنفسجي:** عندما تقتل أنت شخصاً آخر
- **التصميم الذهبي:** عندما يتم قتلك

#### إعدادات إضافية:
- **لون النص:** لون النصوص المعروضة
- **شفافية الظل:** قوة الظلال حول العناصر

### أزرار التحكم:
- **تطبيق:** تطبيق الألوان الجديدة فوراً
- **إعادة تعيين:** العودة للألوان الافتراضية
- **حفظ:** حفظ الإعدادات الحالية

## الأوامر المتاحة

| الأمر | الوصف | المفتاح الافتراضي |
|-------|--------|------------------|
| `/killfeed` | إظهار/إخفاء الـ killfeed | - |
| `/killfeedcolors` | فتح قائمة إعدادات الألوان | F9 |

## التحديثات التقنية

### الملفات المحدثة:
- `html/index.html` - إضافة واجهة إعدادات الألوان
- `html/style.css` - تحسينات بصرية وتأثيرات جديدة
- `html/main.js` - وظائف إدارة الألوان والتأثيرات
- `client.lua` - أوامر جديدة للتحكم

### الميزات التقنية:
- حفظ الإعدادات في localStorage
- تطبيق الألوان ديناميكياً
- تأثيرات CSS3 متقدمة
- واجهة مستخدم responsive

## المتطلبات
- FiveM Server
- المتصفح يدعم CSS3 و HTML5

## الدعم
للدعم والاستفسارات: https://discord.gg/c4c72NGwE2

---
**تطوير:** CB_STORE ✦
