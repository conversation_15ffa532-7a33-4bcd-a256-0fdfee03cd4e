# CB Killfeed - Enhanced Version

## الميزات الجديدة

### 🎨 قائمة إعدادات الألوان المحسنة
- **صلاحية محدودة:** متاحة فقط للمسؤولين مع صلاحية `server.owner`
- **تصميم احترافي:** واجهة مستخدم متطورة مع بطاقات تفاعلية
- **تخصيص شامل:** ألوان الخلفية لكل نوع قتل مع تحكم في الشفافية
- **إعدادات متقدمة:** تحكم في لون النص وشفافية الظلال
- **حفظ ذكي:** حفظ الإعدادات محلياً مع إشعارات تأكيد

### 📍 الموقع الجديد
- تم نقل الـ killfeed إلى الجانب الأيسر من الشاشة
- تصميم محسن مع تأثيرات بصرية

### ✨ التحسينات البصرية
- تأثيرات انتقالية سلسة
- ظلال وتأثيرات ضوئية
- تأثيرات خاصة للقتلات المميزة (headshot, noscope, driveby)
- تأثير نبضة للقتلات الجديدة

## كيفية الاستخدام

### فتح قائمة الألوان
يمكنك فتح قائمة إعدادات الألوان بعدة طرق:

**للاختبار والتطوير:**
1. **الأمر:** `/killfeedtest` (يعمل بدون صلاحيات)
2. **الضغط على O** (إذا كان bypassPermission = true)

**للإنتاج (مع الصلاحيات):**
1. **الضغط على O** (للمسؤولين)
2. **كتابة الأمر:** `/killfeedcolors` (للمسؤولين)
3. **الضغط على أيقونة الإعدادات** 🎨 في أعلى يمين الشاشة

**الصلاحيات المقبولة:**
- `server.owner`
- `admin`
- `killfeed.colors`
- أو تفعيل `Config.enableDemo`

⚠️ **للمطورين:** غير `bypassPermission = false` في client.lua للإنتاج

### 🎮 التحسينات البصرية الجديدة
- **تصميم متطور:** خلفيات متدرجة مع تأثيرات blur وbackdrop-filter
- **تأثيرات حركية:** انتقالات سلسة وتأثيرات hover متقدمة
- **تأثيرات خاصة:**
  - تأثير أحمر للـ headshots
  - تأثير أزرق للـ noscopes
  - تأثير ذهبي للـ driveby kills
- **تأثيرات الإضاءة:** ظلال ديناميكية وتأثيرات glow
- **تحسينات الأيقونات:** تأثيرات خاصة لكل نوع سلاح وأيقونة

### 📏 عرض المسافة المحسن
- **تصنيف ذكي للمسافات:**
  - 🔴 **قريب (0-25م):** لون أحمر للقتلات القريبة
  - 🟡 **متوسط (26-100م):** لون أصفر للمسافات المتوسطة
  - 🟢 **بعيد (+100م):** لون أخضر للقتلات البعيدة
- **تصميم تفاعلي:** أيقونة مسطرة وتأثيرات hover
- **عرض واضح:** المسافة بالمتر مع تأثيرات بصرية مميزة

### إعدادات الألوان المتاحة

#### ألوان الخلفية:
- **التصميم الأسود:** للقتلات العادية
- **التصميم البنفسجي:** عندما تقتل أنت شخصاً آخر
- **التصميم الذهبي:** عندما يتم قتلك

#### إعدادات إضافية:
- **لون النص:** لون النصوص المعروضة
- **شفافية الظل:** قوة الظلال حول العناصر

### أزرار التحكم:
- **تطبيق:** تطبيق الألوان الجديدة فوراً
- **إعادة تعيين:** العودة للألوان الافتراضية
- **حفظ:** حفظ الإعدادات الحالية

## الأوامر المتاحة

| الأمر | الوصف | المفتاح الافتراضي |
|-------|--------|------------------|
| `/killfeed` | إظهار/إخفاء الـ killfeed | - |
| `/killfeedcolors` | فتح قائمة إعدادات الألوان | O |

## التحديثات التقنية

### الملفات المحدثة:
- `html/index.html` - إضافة واجهة إعدادات الألوان
- `html/style.css` - تحسينات بصرية وتأثيرات جديدة
- `html/main.js` - وظائف إدارة الألوان والتأثيرات
- `client.lua` - أوامر جديدة للتحكم

### الميزات التقنية:
- حفظ الإعدادات في localStorage
- تطبيق الألوان ديناميكياً
- تأثيرات CSS3 متقدمة
- واجهة مستخدم responsive

## المتطلبات
- FiveM Server
- المتصفح يدعم CSS3 و HTML5

## الدعم
للدعم والاستفسارات: https://discord.gg/c4c72NGwE2

---
**تطوير:** CB_STORE ✦
