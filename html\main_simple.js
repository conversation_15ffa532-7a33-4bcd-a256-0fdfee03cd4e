// Simple KillFeed - Working Version
let showTime = 7500;
let maxLines = 8;
let textColor = "255, 255, 255";

// Basic initialization
window.onload = () => {
	console.log('Simple KillFeed loaded');
	window.addEventListener('message', onEventReceived);
	
	// Show killfeed container
	$("#killfeed-container").show();
};

// Event handler
function onEventReceived(info) {
	let event = info.data;
	console.log('Event received:', event);
	
	if (event.action == "addKill") {
		addKill(event.data);
	} else if (event.action == "toggleKillfeed") {
		if (event.data.state == true) {
			$("#killfeed-container").show();
		} else {
			$("#killfeed-container").hide();
		}
	} else if (event.action == "setConfig") {
		showTime = event.data.showTime;
		maxLines = event.data.maxLines;
	} else {
		console.log("Unknown action:", event.action);
	}
}

// Simple kill function
function addKill(data) {
	console.log('Adding kill:', data);
	
	if (!data) {
		console.error('No data provided');
		return;
	}

	const id = data.id || Math.random().toString(36).substring(2, 11);
	const image = data.image || '';
	const design = data.design || 'black-design';
	const headshot = data.headshot || false;
	const noScoped = data.noScoped || false;
	const driveBy = data.driveBy || false;
	const dist = data.dist || false;

	let victim = data.victim || {};
	let killer = data.killer || {};

	// Build HTML string
	let html = `<div data-line-id="${id}" class="kill-line">`;
	html += `<div data-id="${id}" class="kill-container ${design}">`;

	// Killer info
	if (killer.name) {
		if (killer.tag) {
			html += `<p class="text tag" style="color: rgb(${textColor});">${killer.tag}</p>`;
		}
		html += `<p class="text name" style="color: rgb(${textColor});">${killer.name}</p>`;
	}

	// Weapon
	if (image) {
		html += `<img class="weapon-image" src="images/${image}.png" alt="${image}">`;
	}

	// Icons
	if (headshot) {
		html += `<img class="icon-image" src="images/headshot.png" alt="headshot">`;
	}
	if (noScoped) {
		html += `<img class="icon-image" src="images/noscoped.png" alt="noscoped">`;
	}
	if (driveBy) {
		html += `<img class="icon-image" src="images/${driveBy}.png" alt="driveby">`;
	}

	// Victim info
	if (victim.name) {
		if (victim.tag) {
			html += `<p class="text tag" style="color: rgb(${textColor});">${victim.tag}</p>`;
		}
		html += `<p class="text name" style="color: rgb(${textColor});">${victim.name}</p>`;
		
		if (dist) {
			html += `<p class="text dist" style="color: rgb(${textColor});">${dist}</p>`;
		}
	}

	html += '</div></div>';

	// Add to feed
	appendToFeed(id, html);
}

// Simple append function
function appendToFeed(id, html) {
	console.log('Appending to feed:', id);
	
	// Remove excess items
	if ($("#killfeed-container").children().length >= maxLines) {
		$("#killfeed-container").children().first().remove();
	}

	// Add new item
	$("#killfeed-container").append(html);

	// Find the added elements
	let killLine = $(`.kill-line[data-line-id="${id}"]`);
	
	if (killLine.length > 0) {
		console.log('Kill line found, animating...');
		
		// Show with animation
		killLine.hide().fadeIn(300);
		
		// Remove after delay
		setTimeout(() => {
			killLine.fadeOut(500, () => {
				killLine.remove();
				console.log('Kill removed:', id);
			});
		}, showTime);
	} else {
		console.error('Kill line not found for id:', id);
	}
}

// Test function
function testKill() {
	const testData = {
		id: "test_" + Date.now(),
		image: "weapon_pistol",
		design: "teal-design",
		headshot: true,
		killer: { name: "TestKiller", tag: "[TEST]" },
		victim: { name: "TestVictim", tag: "[TGT]" },
		dist: "25 m"
	};
	
	addKill(testData);
}

// Make test function global
window.testKill = testKill;

console.log('Simple KillFeed script loaded');
